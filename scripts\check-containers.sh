#!/bin/bash
# Script to check Docker container status

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

echo -e "${CYAN}Checking Docker container status...${NC}"

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo -e "${RED}❌ Docker is not running. Please start Docker.${NC}"
    exit 1
else
    echo -e "${GREEN}✅ Docker is running${NC}"
fi

# List all containers
echo -e "\n${YELLOW}Listing all containers:${NC}"
docker ps -a

# Check container logs
echo -e "\n${YELLOW}Checking container logs:${NC}"

containers=$(docker-compose ps --services 2>/dev/null)
if [ $? -ne 0 ]; then
    echo -e "${RED}❌ Error getting container list${NC}"
else
    for container in $containers; do
        if [ -n "$container" ]; then
            echo -e "\n${<PERSON><PERSON><PERSON>}Logs for $container:${NC}"
            docker-compose logs --tail=20 $container
        fi
    done
fi

# Check container health
echo -e "\n${YELLOW}Checking container health:${NC}"
docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"

# Check Docker networks
echo -e "\n${YELLOW}Checking Docker networks:${NC}"
docker network ls

# Check Docker volumes
echo -e "\n${YELLOW}Checking Docker volumes:${NC}"
docker volume ls

echo -e "\n${CYAN}Container check complete!${NC}"