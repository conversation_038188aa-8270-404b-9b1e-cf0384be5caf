DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql',
        'NAME': 'myapp',  # Database name
        'USER': 'myappuser',  # User name
        'PASSWORD': 'myapppassword',
        'HOST': 'db',  # Ensure this matches the service name in docker-compose.yml
        'PORT': '5432',
    }
}

ALLOWED_HOSTS = ['*']  # Allow all hosts for testing

MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'my_app.csrf_middleware.CustomCSRFMiddleware',  # Use your custom middleware instead of Django's default
    # 'django.middleware.csrf.CsrfViewMiddleware',  # Comment out the default middleware
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
]
