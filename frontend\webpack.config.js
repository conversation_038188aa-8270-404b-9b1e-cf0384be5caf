const path = require('path');
const webpack = require('webpack');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');

// Determine if we should use real API
const useRealApi = process.env.REACT_APP_USE_REAL_API === 'true';

module.exports = {
  // Entry point for the application
  entry: {
    main: ['./src/index.js']
  },

  // Output configuration
  output: {
    path: path.resolve(__dirname, 'build'),
    publicPath: '/',
    filename: 'static/js/[name].[contenthash:8].js',
    chunkFilename: 'static/js/[name].[contenthash:8].chunk.js',
    // Removed chunkFormat: 'array-push' to fix build error
  },

  // Module rules for different file types
  module: {
    rules: [
      {
        test: /\.(js|jsx)$/,
        exclude: /node_modules/,
        use: {
          loader: 'babel-loader',
          options: {
            presets: ['@babel/preset-env', '@babel/preset-react']
          }
        }
      },
      {
        test: /\.css$/,
        use: [MiniCssExtractPlugin.loader, 'css-loader']
      },
      {
        test: /\.(png|svg|jpg|jpeg|gif)$/i,
        type: 'asset/resource',
        generator: {
          filename: 'static/media/[name].[hash:8][ext]'
        }
      }
    ]
  },

  // Development server configuration
  devServer: {
    host: '0.0.0.0',
    port: 3000,
    historyApiFallback: true,
    static: {
      directory: path.join(__dirname, 'public'),
      watch: false, // Disable file watching to prevent I/O errors
      staticOptions: {
        // Exclude index.html from static serving so webpack can serve its generated version
        ignore: ['**/index.html']
      }
    },
    hot: true,
    allowedHosts: 'all',
    // Only add proxy when using real API
    ...(useRealApi ? {
      proxy: {
        '/api': {
          target: process.env.API_TARGET || 'http://localhost:8000',
          changeOrigin: true,
          pathRewrite: { '^/api': '/api' },
          onProxyReq: (proxyReq) => {
            console.log('Proxying to:', proxyReq.path);
          },
          onError: (err, _req, res) => {
            console.error('Proxy error:', err);
            if (res && !res.headersSent) {
              res.writeHead(503, {
                'Content-Type': 'application/json',
              });
              res.end(JSON.stringify({
                error: 'Backend service unavailable',
                message: 'The backend service is currently unavailable. Please try again later.'
              }));
            }
          }
        },
        '/ws': {
          target: process.env.WEBSOCKET_TARGET || 'ws://localhost:8765',
          changeOrigin: true,
          ws: true,
          headers: {
            'Upgrade': 'websocket',
            'Connection': 'upgrade',
          },
          logLevel: 'debug'
        }
      }
    } : {})
  },

  // Plugins
  plugins: [
    new HtmlWebpackPlugin({
      template: './public/index.html',
      filename: 'index.html'
    }),
    new MiniCssExtractPlugin({
      filename: 'static/css/[name].[contenthash:8].css'
    }),
    new webpack.ProvidePlugin({
      process: 'process/browser',
      Buffer: ['buffer', 'Buffer']
    }),
    // Define process.env for client-side code
    new webpack.DefinePlugin({
      'process.env': JSON.stringify(process.env)
    }),
    // Explicitly provide process/browser
    new webpack.ProvidePlugin({
      process: 'process/browser'
    })
  ],

  // Resolve configuration
  // Optimization configuration for better chunk splitting
  optimization: {
    splitChunks: {
      chunks: 'all',
      cacheGroups: {
        vendor: {
          test: /[\\/]node_modules[\\/]/,
          name: 'vendors',
          chunks: 'all',
          priority: 10,
          reuseExistingChunk: true,
        },
        antd: {
          test: /[\\/]node_modules[\\/]antd[\\/]/,
          name: 'antd',
          chunks: 'all',
          priority: 20,
          reuseExistingChunk: true,
        },
        common: {
          name: 'common',
          minChunks: 2,
          chunks: 'all',
          priority: 5,
          reuseExistingChunk: true,
        }
      }
    },
    runtimeChunk: {
      name: 'runtime'
    }
  },

  resolve: {
    extensions: ['.js', '.jsx', '.json'],
    alias: {
      '@': path.resolve(__dirname, 'src'),
      'process/browser': require.resolve('process/browser')
    },
    fallback: {
      "stream": require.resolve("stream-browserify"),
      "buffer": require.resolve("buffer/"),
      "util": require.resolve("util/"),
      "process": require.resolve("process/browser"),
      "path": require.resolve("path-browserify"),
      "os": require.resolve("os-browserify/browser"),
      "crypto": require.resolve("crypto-browserify"),
      "assert": require.resolve("assert/"),
      "url": require.resolve("url/"),
      "querystring": require.resolve("querystring-es3"),
      "constants": require.resolve("constants-browserify"),
      "vm": require.resolve("vm-browserify"),
      "tty": require.resolve("tty-browserify"),
      "fs": false,
      // Explicitly set these to false to avoid axios Node.js adapter issues
      "http": false,
      "https": false,
      "zlib": false
    }
  },
  target: 'web', // Ensure build targets browsers
};






