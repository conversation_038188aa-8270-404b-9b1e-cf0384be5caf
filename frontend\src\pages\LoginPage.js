import React, { useState, useEffect } from 'react';
import { Form, Input, Button, Checkbox, Typography, Alert, Card, Divider, Space, message } from 'antd';
import { UserOutlined, LockOutlined, MailOutlined, GithubOutlined, GoogleOutlined } from '@ant-design/icons';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { useDispatch } from 'react-redux';
import { setUser, clearUser } from '../redux/actions';
import { useAuth } from '../contexts/AuthContext';
import { styled } from '../design-system';
import theme from '../design-system/theme';

const { Title, Text } = Typography;

// Styled components
const LoginContainer = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  padding: ${theme.spacing[4]};
  background-color: ${theme.colors.neutral[100]};
`;

const LoginCard = styled(Card)`
  width: 100%;
  max-width: 480px;
  box-shadow: ${theme.shadows.lg};
  border-radius: ${theme.borderRadius.lg};
`;

const LoginForm = styled(Form)`
  .ant-form-item-label {
    text-align: left;
  }
`;

const LoginButton = styled(Button)`
  width: 100%;
`;

const ForgotPasswordLink = styled(Link)`
  float: right;
`;

const RegisterLink = styled(Text)`
  display: block;
  text-align: center;
  margin-top: 16px;
`;

const SocialButton = styled(Button)`
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
`;

/**
 * LoginPage component
 * Handles user authentication (login and registration)
 */
const LoginPage = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const dispatch = useDispatch();
  const { login, register, isAuthenticated, isLoading, error: authError } = useAuth();
  const [form] = Form.useForm();
  const [isLogin, setIsLogin] = useState(true);
  const [error, setError] = useState(null);

  // Get redirect path from location state or default to '/dashboard'
  const from = location.state?.from?.pathname || '/dashboard';

  // Check if user is already logged in
  useEffect(() => {
    if (isAuthenticated) {
      navigate(from, { replace: true });
    }
  }, [isAuthenticated, navigate, from]);

  // Update local error state when auth error changes
  useEffect(() => {
    setError(authError);
  }, [authError]);

  // Handle form submission
  const handleSubmit = async (values) => {
    setError(null);

    try {
      if (isLogin) {
        // Use real login
        const result = await login(values.email, values.password);

        if (result.success) {
          message.success('Login successful!');
          navigate(from, { replace: true });
        } else {
          setError(result.error || 'Login failed');
        }
      } else {
        // Use real registration
        const userData = {
          username: values.email.split('@')[0], // Use email prefix as username
          email: values.email,
          password: values.password,
          first_name: values.firstName,
          last_name: values.lastName
        };

        const result = await register(userData);

        if (result.success) {
          message.success('Registration successful!');
          navigate(from, { replace: true });
        } else {
          setError(result.error || 'Registration failed');
        }
      }
    } catch (error) {
      console.error('Authentication error:', error);
      setError(error.message || 'An error occurred during authentication');
    }
  };

  // Toggle between login and registration forms
  const toggleAuthMode = () => {
    form.resetFields();
    setIsLogin(!isLogin);
    setError(null);
  };

  // Handle social login
  const handleSocialLogin = (provider) => {
    message.info(`${provider} login is not implemented in this demo`);
  };

  return (
    <LoginContainer>
      <LoginCard>
        <div style={{ textAlign: 'center', marginBottom: theme.spacing[4] }}>
          <Title level={2} style={{ margin: 0 }}>
            {isLogin ? 'Welcome Back' : 'Create Account'}
          </Title>
          <Text type="secondary">
            {isLogin
              ? 'Sign in to continue to App Builder'
              : 'Register to start building amazing applications'}
          </Text>
        </div>

        {error && (
          <Alert
            message="Authentication Error"
            description={error}
            type="error"
            showIcon
            style={{ marginBottom: theme.spacing[4] }}
          />
        )}

        <LoginForm
          form={form}
          name="auth_form"
          layout="vertical"
          onFinish={handleSubmit}
          initialValues={{ remember: true }}
        >
          {!isLogin && (
            <Space style={{ display: 'flex', gap: theme.spacing[3] }}>
              <Form.Item
                name="firstName"
                label="First Name"
                rules={[{ required: true, message: 'Please enter your first name' }]}
                style={{ flex: 1 }}
              >
                <Input placeholder="First Name" />
              </Form.Item>
              <Form.Item
                name="lastName"
                label="Last Name"
                rules={[{ required: true, message: 'Please enter your last name' }]}
                style={{ flex: 1 }}
              >
                <Input placeholder="Last Name" />
              </Form.Item>
            </Space>
          )}

          <Form.Item
            name="email"
            label={isLogin ? "Email or Username" : "Email"}
            rules={[
              { required: true, message: 'Please enter your email' },
              ...(isLogin ? [] : [{ type: 'email', message: 'Please enter a valid email' }])
            ]}
          >
            <Input prefix={<UserOutlined />} placeholder="Email" />
          </Form.Item>

          <Form.Item
            name="password"
            label="Password"
            rules={[{ required: true, message: 'Please enter your password' }]}
          >
            <Input.Password prefix={<LockOutlined />} placeholder="Password" />
          </Form.Item>

          {!isLogin && (
            <Form.Item
              name="confirmPassword"
              label="Confirm Password"
              dependencies={['password']}
              rules={[
                { required: true, message: 'Please confirm your password' },
                ({ getFieldValue }) => ({
                  validator(_, value) {
                    if (!value || getFieldValue('password') === value) {
                      return Promise.resolve();
                    }
                    return Promise.reject(new Error('The two passwords do not match'));
                  },
                }),
              ]}
            >
              <Input.Password prefix={<LockOutlined />} placeholder="Confirm Password" />
            </Form.Item>
          )}

          {isLogin && (
            <Form.Item>
              <Form.Item name="remember" valuePropName="checked" noStyle>
                <Checkbox>Remember me</Checkbox>
              </Form.Item>

              <ForgotPasswordLink to="/forgot-password">
                Forgot password?
              </ForgotPasswordLink>
            </Form.Item>
          )}

          <Form.Item>
            <LoginButton
              type="primary"
              htmlType="submit"
              size="large"
              loading={isLoading}
            >
              {isLogin ? 'Sign In' : 'Create Account'}
            </LoginButton>
          </Form.Item>
        </LoginForm>

        <Divider>
          <Text type="secondary">Or continue with</Text>
        </Divider>

        <Space direction="horizontal" style={{ width: '100%', justifyContent: 'center', gap: theme.spacing[3], marginBottom: theme.spacing[4] }}>
          <SocialButton
            icon={<GoogleOutlined />}
            onClick={() => handleSocialLogin('Google')}
          >
            Google
          </SocialButton>
          <SocialButton
            icon={<GithubOutlined />}
            onClick={() => handleSocialLogin('GitHub')}
          >
            GitHub
          </SocialButton>
        </Space>

        <div style={{ textAlign: 'center' }}>
          <Text type="secondary">
            {isLogin ? "Don't have an account? " : "Already have an account? "}
            <Button type="link" onClick={toggleAuthMode} style={{ padding: 0 }}>
              {isLogin ? 'Sign up now' : 'Sign in'}
            </Button>
          </Text>
        </div>
      </LoginCard>
    </LoginContainer>
  );
};

export default LoginPage;
