import React from 'react';
import styled from 'styled-components';

const SkipLinkButton = styled.a`
  position: absolute;
  top: -40px;
  left: 0;
  background: ${props => {
    // Handle different theme structures
    if (props.theme?.colorPalette?.primary) {
      return props.theme.colorPalette.primary;
    }
    if (props.theme?.colors?.primary?.main) {
      return props.theme.colors.primary.main;
    }
    if (props.theme?.primaryColor) {
      return props.theme.primaryColor;
    }
    // Fallback color
    return '#2563EB';
  }};
  color: white;
  padding: 8px;
  z-index: ${props => props.theme?.zIndex?.tooltip || props.theme?.zIndex?.modal || 1000};
  transition: top 0.3s;

  &:focus {
    top: 0;
  }
`;

/**
 * SkipLink component
 * Provides a way for keyboard users to skip navigation and go directly to main content
 * This is an accessibility feature that is hidden until focused
 */
const SkipLink = ({ targetId = 'main-content' }) => {
  return (
    <SkipLinkButton href={`#${targetId}`}>
      Skip to main content
    </SkipLinkButton>
  );
};

export default SkipLink;
