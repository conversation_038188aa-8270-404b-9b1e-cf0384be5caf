from rest_framework.authtoken.views import ObtainAuthToken
from rest_framework.authtoken.models import Token
from rest_framework.response import Response
from rest_framework import status
from django.contrib.auth.models import User
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
from .auth.jwt_auth import generate_jwt_token
from .error_handling import error_response, handle_exception
from .security import add_security_headers, sanitize_request, get_sanitized_data
from .rate_limiting import rate_limit
import logging

# Set up logger
logger = logging.getLogger(__name__)

class CustomAuthToken(ObtainAuthToken):
    """
    Custom token authentication view that returns more information about the user.
    """
    def post(self, request, *args, **kwargs):
        serializer = self.serializer_class(data=request.data,
                                           context={'request': request})
        if serializer.is_valid():
            user = serializer.validated_data['user']
            token, created = Token.objects.get_or_create(user=user)
            return Response({
                'token': token.key,
                'user_id': user.pk,
                'username': user.username,
                'email': user.email,
                'is_staff': user.is_staff,
                'is_superuser': user.is_superuser,
            })
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

@api_view(['POST'])
@permission_classes([AllowAny])
@handle_exception
@add_security_headers
@sanitize_request
@rate_limit(requests=3, period=3600, scope='ip')  # 3 requests per hour per IP
def register_user(request):
    """
    Register a new user and return a token.
    """
    username = get_sanitized_data(request, 'username')
    email = get_sanitized_data(request, 'email')
    password = get_sanitized_data(request, 'password')
    first_name = get_sanitized_data(request, 'first_name', '')
    last_name = get_sanitized_data(request, 'last_name', '')

    # Validate the request
    if not username or not email or not password:
        return error_response(
            'MISSING_FIELD',
            'Username, email, and password are required',
            status.HTTP_400_BAD_REQUEST
        )

    # Check if the username already exists
    if User.objects.filter(username=username).exists():
        return error_response(
            'ALREADY_EXISTS',
            'Username already exists',
            status.HTTP_400_BAD_REQUEST
        )

    # Check if the email already exists
    if User.objects.filter(email=email).exists():
        return error_response(
            'ALREADY_EXISTS',
            'Email already exists',
            status.HTTP_400_BAD_REQUEST
        )

    # Create the user
    user = User.objects.create_user(
        username=username,
        email=email,
        password=password,
        first_name=first_name,
        last_name=last_name
    )

    # Generate both token types for backward compatibility
    token, created = Token.objects.get_or_create(user=user)
    jwt_token = generate_jwt_token(user)

    # Return the tokens and user info
    return Response({
        'token': token.key,
        'jwt_token': jwt_token,
        'user': {
            'id': user.id,
            'username': user.username,
            'email': user.email,
            'first_name': user.first_name,
            'last_name': user.last_name,
            'is_staff': user.is_staff
        }
    }, status=status.HTTP_201_CREATED)

@api_view(['POST'])
@permission_classes([AllowAny])
@handle_exception
@add_security_headers
@sanitize_request
@rate_limit(requests=5, period=60, scope='ip')  # 5 requests per minute per IP
def jwt_login(request):
    """
    Login a user and return a JWT token.
    """
    username = get_sanitized_data(request, 'username')
    password = get_sanitized_data(request, 'password')

    # Validate the request
    if not username or not password:
        return error_response(
            'MISSING_FIELD',
            'Username and password are required',
            status.HTTP_400_BAD_REQUEST
        )

    # Authenticate the user
    from django.contrib.auth import authenticate
    user = authenticate(username=username, password=password)
    if not user:
        logger.warning(f"Failed login attempt for user: {username}")
        return error_response(
            'INVALID_FIELD',
            'Invalid username or password',
            status.HTTP_401_UNAUTHORIZED
        )

    # Generate both token types for backward compatibility
    token, created = Token.objects.get_or_create(user=user)
    jwt_token = generate_jwt_token(user)

    # Return the tokens and user info
    return Response({
        'token': token.key,
        'jwt_token': jwt_token,
        'user': {
            'id': user.id,
            'username': user.username,
            'email': user.email,
            'first_name': user.first_name,
            'last_name': user.last_name,
            'is_staff': user.is_staff
        }
    })

@api_view(['GET'])
@handle_exception
@add_security_headers
def user_profile(request):
    """
    Get the user profile.
    """
    user = request.user

    # Return the user profile
    return Response({
        'id': user.id,
        'username': user.username,
        'email': user.email,
        'first_name': user.first_name,
        'last_name': user.last_name,
        'is_staff': user.is_staff,
        'date_joined': user.date_joined,
        'last_login': user.last_login
    })

@api_view(['PUT'])
@handle_exception
@add_security_headers
@sanitize_request
def update_profile(request):
    """
    Update the user profile.
    """
    user = request.user
    first_name = request.data.get('first_name')
    last_name = request.data.get('last_name')
    email = request.data.get('email')

    # Update the user profile
    if first_name is not None:
        user.first_name = first_name
    if last_name is not None:
        user.last_name = last_name
    if email is not None:
        # Check if the email already exists
        if User.objects.filter(email=email).exclude(id=user.id).exists():
            return error_response(
                'ALREADY_EXISTS',
                'Email already exists',
                status.HTTP_400_BAD_REQUEST
            )
        user.email = email

    # Save the user
    user.save()

    # Return the updated user profile
    return Response({
        'id': user.id,
        'username': user.username,
        'email': user.email,
        'first_name': user.first_name,
        'last_name': user.last_name,
        'is_staff': user.is_staff,
        'date_joined': user.date_joined,
        'last_login': user.last_login
    })
