import React, { useState, useEffect, useRef } from 'react';
import { <PERSON>, Typography, Button, Space, Divider, Statistic, Progress, Alert, Tabs, Table, Tooltip } from 'antd';
import {
  DashboardOutlined,
  ReloadOutlined,
  WarningOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  LineChartOutlined,
  Bar<PERSON><PERSON>Outlined,
  InfoCircleOutlined
} from '@ant-design/icons';
import { styled } from '../../design-system';
import theme from '../../design-system/theme';

const { Title, Text, Paragraph } = Typography;
const { TabPane } = Tabs;

const MonitorContainer = styled.div`
  padding: ${theme.spacing[3]};
`;

const MetricsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: ${theme.spacing[4]};
  margin-bottom: ${theme.spacing[4]};
`;

const MetricCard = styled(Card)`
  text-align: center;
`;

const ChartContainer = styled.div`
  height: 300px;
  margin-bottom: ${theme.spacing[4]};
  background-color: ${theme.colors.neutral[100]};
  border-radius: ${theme.borderRadius.md};
  display: flex;
  justify-content: center;
  align-items: center;
`;

/**
 * PerformanceMonitor component
 * Monitors and displays application performance metrics
 */
const PerformanceMonitor = () => {
  const [metrics, setMetrics] = useState({
    memory: {
      used: 0,
      total: 0,
      limit: 0
    },
    cpu: {
      usage: 0,
      cores: 0
    },
    network: {
      requests: 0,
      transferred: 0,
      errors: 0
    },
    rendering: {
      fps: 0,
      renderTime: 0
    },
    components: {
      count: 0,
      renderCount: 0
    }
  });
  
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('overview');
  const [error, setError] = useState(null);
  const [lastUpdated, setLastUpdated] = useState(null);
  
  const animationFrameRef = useRef();
  const fpsCounterRef = useRef(0);
  const lastFrameTimeRef = useRef(performance.now());
  const frameTimesRef = useRef([]);
  
  // Simulate collecting performance metrics
  const collectMetrics = () => {
    try {
      setLoading(true);
      
      // Simulate memory metrics
      const memory = window.performance?.memory || {
        usedJSHeapSize: Math.random() * 100 * 1024 * 1024,
        totalJSHeapSize: 200 * 1024 * 1024,
        jsHeapSizeLimit: 500 * 1024 * 1024
      };
      
      // Simulate CPU metrics
      const cpuUsage = Math.random() * 100;
      const cpuCores = navigator.hardwareConcurrency || 4;
      
      // Simulate network metrics
      const networkRequests = Math.floor(Math.random() * 50);
      const networkTransferred = Math.random() * 5 * 1024 * 1024;
      const networkErrors = Math.floor(Math.random() * 3);
      
      // Get component metrics from the DOM
      const componentCount = document.querySelectorAll('[data-component]').length || Math.floor(Math.random() * 20);
      const componentRenderCount = Math.floor(Math.random() * 100);
      
      // Calculate FPS
      const now = performance.now();
      const elapsed = now - lastFrameTimeRef.current;
      lastFrameTimeRef.current = now;
      
      frameTimesRef.current.push(elapsed);
      if (frameTimesRef.current.length > 60) {
        frameTimesRef.current.shift();
      }
      
      const averageFrameTime = frameTimesRef.current.reduce((sum, time) => sum + time, 0) / frameTimesRef.current.length;
      const fps = Math.round(1000 / averageFrameTime);
      
      // Update metrics
      setMetrics({
        memory: {
          used: memory.usedJSHeapSize,
          total: memory.totalJSHeapSize,
          limit: memory.jsHeapSizeLimit
        },
        cpu: {
          usage: cpuUsage,
          cores: cpuCores
        },
        network: {
          requests: networkRequests,
          transferred: networkTransferred,
          errors: networkErrors
        },
        rendering: {
          fps,
          renderTime: averageFrameTime
        },
        components: {
          count: componentCount,
          renderCount: componentRenderCount
        }
      });
      
      setLastUpdated(new Date());
      setLoading(false);
    } catch (error) {
      console.error('Error collecting metrics:', error);
      setError('Failed to collect performance metrics');
      setLoading(false);
    }
  };
  
  // Start collecting metrics on mount
  useEffect(() => {
    collectMetrics();
    
    // Set up animation frame loop for FPS calculation
    const updateFPS = () => {
      fpsCounterRef.current++;
      animationFrameRef.current = requestAnimationFrame(updateFPS);
    };
    
    animationFrameRef.current = requestAnimationFrame(updateFPS);
    
    // Set up interval to update metrics
    const intervalId = setInterval(() => {
      collectMetrics();
    }, 2000);
    
    return () => {
      clearInterval(intervalId);
      cancelAnimationFrame(animationFrameRef.current);
    };
  }, []);
  
  // Format bytes to human-readable format
  const formatBytes = (bytes, decimals = 2) => {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const dm = decimals < 0 ? 0 : decimals;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
  };
  
  // Get status color based on value
  const getStatusColor = (value, thresholds) => {
    if (value >= thresholds.danger) return theme.colors.danger.main;
    if (value >= thresholds.warning) return theme.colors.warning.main;
    return theme.colors.success.main;
  };
  
  // Network requests data
  const networkData = [
    {
      key: '1',
      url: '/api/components',
      method: 'GET',
      status: 200,
      time: '120ms',
      size: '5.2KB'
    },
    {
      key: '2',
      url: '/api/layouts',
      method: 'GET',
      status: 200,
      time: '85ms',
      size: '3.8KB'
    },
    {
      key: '3',
      url: '/api/themes',
      method: 'GET',
      status: 200,
      time: '95ms',
      size: '2.1KB'
    },
    {
      key: '4',
      url: '/api/user',
      method: 'GET',
      status: 200,
      time: '110ms',
      size: '1.5KB'
    },
    {
      key: '5',
      url: '/api/settings',
      method: 'GET',
      status: 404,
      time: '75ms',
      size: '0.5KB'
    }
  ];
  
  // Network requests columns
  const networkColumns = [
    {
      title: 'URL',
      dataIndex: 'url',
      key: 'url'
    },
    {
      title: 'Method',
      dataIndex: 'method',
      key: 'method'
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <Text
          type={status >= 400 ? 'danger' : status >= 300 ? 'warning' : 'success'}
          strong
        >
          {status}
        </Text>
      )
    },
    {
      title: 'Time',
      dataIndex: 'time',
      key: 'time'
    },
    {
      title: 'Size',
      dataIndex: 'size',
      key: 'size'
    }
  ];
  
  return (
    <MonitorContainer>
      <Title level={4}>Performance Monitor</Title>
      <Paragraph>
        Monitor and analyze the performance of your application.
      </Paragraph>
      
      {error && (
        <Alert
          message="Error"
          description={error}
          type="error"
          showIcon
          closable
          onClose={() => setError(null)}
          style={{ marginBottom: theme.spacing[4] }}
        />
      )}
      
      <Space style={{ marginBottom: theme.spacing[4] }}>
        <Button
          icon={<ReloadOutlined />}
          onClick={collectMetrics}
          loading={loading}
        >
          Refresh Metrics
        </Button>
        
        {lastUpdated && (
          <Text type="secondary">
            Last updated: {lastUpdated.toLocaleTimeString()}
          </Text>
        )}
      </Space>
      
      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane tab="Overview" key="overview">
          <MetricsGrid>
            <MetricCard>
              <Statistic
                title="Memory Usage"
                value={formatBytes(metrics.memory.used)}
                suffix={` / ${formatBytes(metrics.memory.total)}`}
              />
              <Progress
                percent={Math.round((metrics.memory.used / metrics.memory.total) * 100)}
                status={
                  metrics.memory.used / metrics.memory.total > 0.9
                    ? 'exception'
                    : metrics.memory.used / metrics.memory.total > 0.7
                    ? 'warning'
                    : 'normal'
                }
                strokeColor={getStatusColor(
                  metrics.memory.used / metrics.memory.total * 100,
                  { warning: 70, danger: 90 }
                )}
              />
            </MetricCard>
            
            <MetricCard>
              <Statistic
                title="CPU Usage"
                value={Math.round(metrics.cpu.usage)}
                suffix="%"
              />
              <Progress
                percent={Math.round(metrics.cpu.usage)}
                status={
                  metrics.cpu.usage > 90
                    ? 'exception'
                    : metrics.cpu.usage > 70
                    ? 'warning'
                    : 'normal'
                }
                strokeColor={getStatusColor(
                  metrics.cpu.usage,
                  { warning: 70, danger: 90 }
                )}
              />
            </MetricCard>
            
            <MetricCard>
              <Statistic
                title="FPS"
                value={metrics.rendering.fps}
                suffix="fps"
              />
              <Progress
                percent={Math.min(100, Math.round(metrics.rendering.fps / 60 * 100))}
                status={
                  metrics.rendering.fps < 30
                    ? 'exception'
                    : metrics.rendering.fps < 50
                    ? 'warning'
                    : 'normal'
                }
                strokeColor={getStatusColor(
                  metrics.rendering.fps,
                  { warning: 50, danger: 30 },
                  true
                )}
              />
            </MetricCard>
            
            <MetricCard>
              <Statistic
                title="Network Requests"
                value={metrics.network.requests}
              />
              <div style={{ marginTop: theme.spacing[2] }}>
                <Text type={metrics.network.errors > 0 ? 'danger' : 'success'}>
                  {metrics.network.errors} errors
                </Text>
                <br />
                <Text type="secondary">
                  {formatBytes(metrics.network.transferred)} transferred
                </Text>
              </div>
            </MetricCard>
          </MetricsGrid>
          
          <ChartContainer>
            <Text type="secondary">
              Performance charts will be available in a future update
            </Text>
          </ChartContainer>
        </TabPane>
        
        <TabPane tab="Network" key="network">
          <Table
            dataSource={networkData}
            columns={networkColumns}
            pagination={false}
            size="small"
          />
        </TabPane>
        
        <TabPane tab="Components" key="components">
          <MetricsGrid>
            <MetricCard>
              <Statistic
                title="Component Count"
                value={metrics.components.count}
              />
            </MetricCard>
            
            <MetricCard>
              <Statistic
                title="Render Count"
                value={metrics.components.renderCount}
              />
            </MetricCard>
            
            <MetricCard>
              <Statistic
                title="Average Render Time"
                value={Math.round(metrics.rendering.renderTime)}
                suffix="ms"
              />
            </MetricCard>
          </MetricsGrid>
          
          <Alert
            message="Component Performance"
            description="Detailed component performance metrics will be available in a future update."
            type="info"
            showIcon
          />
        </TabPane>
      </Tabs>
    </MonitorContainer>
  );
};

export default PerformanceMonitor;
