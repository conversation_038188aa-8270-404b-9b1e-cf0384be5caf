"""
Security utilities for the API.
"""
import logging
import re
from functools import wraps
from django.http import JsonResponse
from .error_handling import error_response

# Set up logger
logger = logging.getLogger(__name__)

def sanitize_input(input_str):
    """
    Sanitize input to prevent XSS and other injection attacks.
    
    Args:
        input_str (str): The input string to sanitize
        
    Returns:
        str: The sanitized input string
    """
    if not isinstance(input_str, str):
        return input_str
    
    # Remove script tags
    sanitized = re.sub(r'<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>', '', input_str, flags=re.IGNORECASE)
    
    # Remove other potentially dangerous tags
    sanitized = re.sub(r'<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>', '', sanitized, flags=re.IGNORECASE)
    sanitized = re.sub(r'<object\b[^<]*(?:(?!<\/object>)<[^<]*)*<\/object>', '', sanitized, flags=re.IGNORECASE)
    sanitized = re.sub(r'<embed\b[^<]*(?:(?!<\/embed>)<[^<]*)*<\/embed>', '', sanitized, flags=re.IGNORECASE)
    
    # Remove on* attributes
    sanitized = re.sub(r'on\w+\s*=\s*["\'][^"\']*["\']', '', sanitized, flags=re.IGNORECASE)
    
    # Remove javascript: URLs
    sanitized = re.sub(r'javascript:', 'x-javascript:', sanitized, flags=re.IGNORECASE)
    
    return sanitized

def sanitize_json(json_data):
    """
    Recursively sanitize JSON data.

    Args:
        json_data (dict or list): The JSON data to sanitize

    Returns:
        dict or list: The sanitized JSON data
    """
    if isinstance(json_data, dict):
        return {k: sanitize_json(v) for k, v in json_data.items()}
    elif isinstance(json_data, list):
        return [sanitize_json(item) for item in json_data]
    elif isinstance(json_data, str):
        return sanitize_input(json_data)
    else:
        return json_data

def get_sanitized_data(request, field_name, default=None):
    """
    Get sanitized data from request.data.

    Args:
        request: The request object
        field_name (str): The field name to get
        default: Default value if field is not found

    Returns:
        The sanitized field value
    """
    if hasattr(request, 'data') and request.data:
        value = request.data.get(field_name, default)
        if isinstance(value, str):
            return sanitize_input(value)
        elif isinstance(value, (dict, list)):
            return sanitize_json(value)
        return value
    return default

def sanitize_request(view_func):
    """
    Decorator to sanitize request data.

    Args:
        view_func: The view function to decorate

    Returns:
        function: The decorated function
    """
    @wraps(view_func)
    def wrapper(request, *args, **kwargs):
        # Note: We can't modify request.data directly as it's read-only in DRF
        # The sanitization will be handled in the view functions themselves
        # This decorator is kept for compatibility but doesn't modify request.data

        return view_func(request, *args, **kwargs)

    return wrapper

def add_security_headers(view_func):
    """
    Decorator to add security headers to responses.
    
    Args:
        view_func: The view function to decorate
        
    Returns:
        function: The decorated function
    """
    @wraps(view_func)
    def wrapper(request, *args, **kwargs):
        response = view_func(request, *args, **kwargs)
        
        # Add security headers
        response['X-Content-Type-Options'] = 'nosniff'
        response['X-Frame-Options'] = 'DENY'
        response['X-XSS-Protection'] = '1; mode=block'
        response['Referrer-Policy'] = 'strict-origin-when-cross-origin'
        response['Permissions-Policy'] = 'geolocation=(), microphone=(), camera=()'
        
        # Add Content Security Policy header
        csp_directives = [
            "default-src 'self'",
            "script-src 'self' 'unsafe-inline'",
            "style-src 'self' 'unsafe-inline'",
            "img-src 'self' data: https:",
            "font-src 'self'",
            "connect-src 'self' wss: ws:",
            "frame-ancestors 'none'",
            "base-uri 'self'",
            "form-action 'self'"
        ]
        response['Content-Security-Policy'] = '; '.join(csp_directives)
        
        return response
    
    return wrapper
