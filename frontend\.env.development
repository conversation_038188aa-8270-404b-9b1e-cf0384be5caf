# Development environment configuration for real backend API

# Enable real API instead of mocks
REACT_APP_USE_REAL_API=true

# Backend API configuration
REACT_APP_API_BASE_URL=http://localhost:8000
REACT_APP_BACKEND_HOST=localhost
API_TARGET=http://localhost:8000

# WebSocket configuration
REACT_APP_WEBSOCKET_URL=ws://localhost:8765
WEBSOCKET_TARGET=ws://localhost:8765

# Development settings
NODE_ENV=development
GENERATE_SOURCEMAP=true
FAST_REFRESH=true

# Disable npm fund messages
DISABLE_ESLINT_PLUGIN=false
SKIP_PREFLIGHT_CHECK=true

# Performance settings
CHOKIDAR_USEPOLLING=false
WATCHPACK_POLLING=false
