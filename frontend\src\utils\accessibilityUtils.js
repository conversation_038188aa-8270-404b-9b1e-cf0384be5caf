/**
 * Accessibility Utilities
 * 
 * This file contains utilities for accessibility features.
 */

/**
 * Add SVG filters for color blind modes to the document
 */
export function addColorBlindFilters() {
  // Check if filters already exist
  if (document.getElementById('accessibility-filters')) {
    return;
  }
  
  // Create SVG element
  const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
  svg.setAttribute('id', 'accessibility-filters');
  svg.setAttribute('aria-hidden', 'true');
  svg.style.position = 'absolute';
  svg.style.width = '0';
  svg.style.height = '0';
  svg.style.overflow = 'hidden';
  
  // Add filters
  svg.innerHTML = `
    <!-- Protanopia (Red-Blind) Filter -->
    <filter id="protanopia-filter">
      <feColorMatrix
        in="SourceGraphic"
        type="matrix"
        values="0.567, 0.433, 0,     0, 0
                0.558, 0.442, 0,     0, 0
                0,     0.242, 0.758, 0, 0
                0,     0,     0,     1, 0"
      />
    </filter>
    
    <!-- Deuteranopia (Green-Blind) Filter -->
    <filter id="deuteranopia-filter">
      <feColorMatrix
        in="SourceGraphic"
        type="matrix"
        values="0.625, 0.375, 0,   0, 0
                0.7,   0.3,   0,   0, 0
                0,     0.3,   0.7, 0, 0
                0,     0,     0,   1, 0"
      />
    </filter>
    
    <!-- Tritanopia (Blue-Blind) Filter -->
    <filter id="tritanopia-filter">
      <feColorMatrix
        in="SourceGraphic"
        type="matrix"
        values="0.95, 0.05,  0,     0, 0
                0,    0.433, 0.567, 0, 0
                0,    0.475, 0.525, 0, 0
                0,    0,     0,     1, 0"
      />
    </filter>
  `;
  
  // Add to document
  document.body.appendChild(svg);
}

/**
 * Initialize accessibility features
 */
export function initAccessibility() {
  // Add color blind filters
  addColorBlindFilters();
  
  // Add skip link
  addSkipLink();
  
  // Add keyboard navigation
  initKeyboardNavigation();
  
  // Check for system preferences
  checkSystemPreferences();
}

/**
 * Add skip link to the document
 */
function addSkipLink() {
  // Check if skip link already exists
  if (document.querySelector('.skip-link')) {
    return;
  }
  
  // Create skip link
  const skipLink = document.createElement('a');
  skipLink.href = '#main-content';
  skipLink.className = 'skip-link';
  skipLink.textContent = 'Skip to main content';
  
  // Add to document
  document.body.insertBefore(skipLink, document.body.firstChild);
}

/**
 * Initialize keyboard navigation
 */
function initKeyboardNavigation() {
  // Add keyboard navigation class to body
  document.body.classList.add('keyboard-navigation');
  
  // Add event listeners
  document.addEventListener('keydown', handleKeyDown);
  document.addEventListener('mousedown', handleMouseDown);
  
  // Handle keyboard navigation
  function handleKeyDown(event) {
    if (event.key === 'Tab') {
      document.body.classList.add('keyboard-navigation');
    }
  }
  
  // Handle mouse navigation
  function handleMouseDown() {
    document.body.classList.remove('keyboard-navigation');
  }
}

/**
 * Check for system preferences
 */
function checkSystemPreferences() {
  if (window.matchMedia) {
    // Check for prefers-reduced-motion
    const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)');
    if (prefersReducedMotion.matches && !localStorage.getItem('accessibility_reducedMotion')) {
      document.body.classList.add('reduced-motion');
      localStorage.setItem('accessibility_reducedMotion', 'true');
    }
    
    // Check for prefers-contrast
    const prefersContrast = window.matchMedia('(prefers-contrast: more)');
    if (prefersContrast.matches && !localStorage.getItem('accessibility_highContrast')) {
      document.body.classList.add('high-contrast');
      localStorage.setItem('accessibility_highContrast', 'true');
    }
    
    // Check for prefers-color-scheme
    const prefersDarkMode = window.matchMedia('(prefers-color-scheme: dark)');
    if (prefersDarkMode.matches && !localStorage.getItem('theme')) {
      document.body.dataset.theme = 'dark';
      localStorage.setItem('theme', 'dark');
    }
  }
}

export default {
  addColorBlindFilters,
  initAccessibility
};
