import React, { useState, useEffect, useRef } from 'react';
import { useSelector } from 'react-redux';
import { message, Tabs, Button, Input, Switch, Card, Typography, Space, Divider, Alert } from 'antd';
import {
  SendOutlined,
  ReloadOutlined,
  DisconnectOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  SettingOutlined,
  ClearOutlined,
  InfoCircleOutlined,
  CodeOutlined,
  BugOutlined
} from '@ant-design/icons';

import { styled } from '../../design-system';
import theme from '../../design-system/theme';

// Import custom hook
import useWebSocketConnection from '../../hooks/useWebSocketConnection';

const { TabPane } = Tabs;
const { TextArea } = Input;
const { Title, Text, Paragraph } = Typography;

const WebSocketManagerContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${theme.spacing[4]};
`;

const ConnectionStatus = styled.div`
  display: flex;
  align-items: center;
  gap: ${theme.spacing[2]};
  padding: ${theme.spacing[3]};
  border-radius: ${theme.borderRadius.md};
  background-color: ${props => props.connected ? theme.colors.success.light : theme.colors.error.light};
  color: ${props => props.connected ? theme.colors.success.dark : theme.colors.error.dark};
`;

const MessageList = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${theme.spacing[2]};
  max-height: 400px;
  overflow-y: auto;
  padding: ${theme.spacing[2]};
  border: 1px solid ${theme.colors.neutral[200]};
  border-radius: ${theme.borderRadius.md};
  background-color: ${theme.colors.neutral[50]};
`;

const Message = styled.div`
  padding: ${theme.spacing[2]} ${theme.spacing[3]};
  border-radius: ${theme.borderRadius.md};
  background-color: ${props => props.type === 'sent' ? theme.colors.primary.light : 'white'};
  border-left: 4px solid ${props => props.type === 'sent' ? theme.colors.primary.main : theme.colors.secondary.main};
  box-shadow: ${theme.shadows.sm};

  .message-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: ${theme.spacing[1]};
    font-size: ${theme.typography.fontSize.sm};
    color: ${theme.colors.neutral[500]};
  }

  .timestamp {
    font-family: monospace;
    font-size: ${theme.typography.fontSize.xs};
    color: ${theme.colors.neutral[600]};
    background-color: ${theme.colors.neutral[100]};
    padding: 2px 4px;
    border-radius: 3px;
  }

  .message-content {
    word-break: break-word;
  }

  pre {
    background-color: ${theme.colors.neutral[100]};
    padding: ${theme.spacing[2]};
    border-radius: ${theme.borderRadius.sm};
    overflow-x: auto;
    font-family: ${theme.typography.fontFamily.code};
    font-size: ${theme.typography.fontSize.sm};
    margin: ${theme.spacing[2]} 0 0 0;
  }
`;

const MessageInput = styled.div`
  display: flex;
  gap: ${theme.spacing[2]};
`;

const SettingsPanel = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${theme.spacing[3]};
  padding: ${theme.spacing[3]};
  border: 1px solid ${theme.colors.neutral[200]};
  border-radius: ${theme.borderRadius.md};
  background-color: white;
`;

const SettingsGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${theme.spacing[2]};
`;

const MessageTemplates = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: ${theme.spacing[2]};
  margin-bottom: ${theme.spacing[3]};
`;

const DiagnosticsPanel = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${theme.spacing[3]};
  padding: ${theme.spacing[3]};
  border: 1px solid ${theme.colors.neutral[200]};
  border-radius: ${theme.borderRadius.md};
  background-color: white;
`;

const DiagnosticsItem = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${theme.spacing[1]};
  padding: ${theme.spacing[2]};
  border: 1px solid ${theme.colors.neutral[200]};
  border-radius: ${theme.borderRadius.md};
  background-color: ${theme.colors.neutral[50]};
`;

/**
 * Legacy WebSocket Manager with error handling for code 1006
 */
const LegacyWebSocketManager = () => {
  const { url: storeUrl } = useSelector(state => state.websocket);

  const [activeTab, setActiveTab] = useState('messages');
  const [wsUrl, setWsUrl] = useState(storeUrl || 'ws://localhost:8000/ws/test/');
  const [messageText, setMessageText] = useState('');
  const [autoReconnect, setAutoReconnect] = useState(true);
  const [showTimestamp, setShowTimestamp] = useState(true);
  const [heartbeatEnabled, setHeartbeatEnabled] = useState(true);

  const messagesEndRef = useRef(null);

  // Use the WebSocket hook
  const {
    connected,
    connecting,
    reconnectAttempt,
    messages,
    error,
    closeInfo,
    connect,
    disconnect,
    sendMessage,
    clearMessages,
    resetConnection
  } = useWebSocketConnection({
    url: wsUrl,
    autoConnect: false,
    autoReconnect,
    debug: true,
    updateRedux: true,
    heartbeatInterval: heartbeatEnabled ? 30000 : 0,
    reconnectInterval: 2000,
    maxReconnectAttempts: 5
  });

  // Auto-scroll to bottom of messages
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages]);

  // Handle connect button click
  const handleConnect = () => {
    try {
      // Validate WebSocket URL
      if (!wsUrl.trim().startsWith('ws://') && !wsUrl.trim().startsWith('wss://')) {
        message.error('Invalid WebSocket URL. It should start with ws:// or wss://');
        return;
      }

      connect();
      message.info('Connecting to WebSocket...');
    } catch (error) {
      message.error(`Connection error: ${error.message}`);
    }
  };

  // Handle disconnect button click
  const handleDisconnect = () => {
    try {
      disconnect();
      message.info('Disconnected from WebSocket');
    } catch (error) {
      message.error(`Disconnect error: ${error.message}`);
    }
  };

  // Handle reconnect with new URL
  const handleReconnect = () => {
    try {
      resetConnection();
      setTimeout(() => {
        connect();
        message.info('Reconnecting to WebSocket...');
      }, 500);
    } catch (error) {
      message.error(`Reconnection error: ${error.message}`);
    }
  };

  // Handle send message button click
  const handleSendMessage = () => {
    if (!messageText.trim() || !connected) return;

    try {
      // Try to parse as JSON if it looks like JSON
      let messageData;
      if (messageText.trim().startsWith('{') || messageText.trim().startsWith('[')) {
        messageData = JSON.parse(messageText);
      } else {
        messageData = { message: messageText };
      }

      // Send the message
      sendMessage(messageData);

      // Clear the input
      setMessageText('');
    } catch (error) {
      message.error(`Error sending message: ${error.message}`);
    }
  };

  // Handle key down in message input
  const handleKeyDown = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  // Format timestamp
  const formatTimestamp = (timestamp) => {
    if (!showTimestamp) return null;

    try {
      // Parse the timestamp string to a Date object
      const date = new Date(timestamp);

      // Check if the date is valid
      if (isNaN(date.getTime())) {
        console.warn('Invalid timestamp:', timestamp);
        return null;
      }

      // Format the date as YYYY-MM-DD HH:MM:SS.mmm
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      const seconds = String(date.getSeconds()).padStart(2, '0');
      const milliseconds = String(date.getMilliseconds()).padStart(3, '0');

      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}.${milliseconds}`;
    } catch (error) {
      console.error('Error formatting timestamp:', error);
      return null;
    }
  };

  // Check if a string is JSON
  const isJsonString = (str) => {
    try {
      const json = JSON.parse(str);
      return typeof json === 'object';
    } catch (e) {
      return false;
    }
  };

  // Render message content
  const renderMessageContent = (content) => {
    if (typeof content === 'string' && isJsonString(content)) {
      return (
        <>
          <div className="message-content">JSON Message</div>
          <pre>{JSON.stringify(JSON.parse(content), null, 2)}</pre>
        </>
      );
    } else if (typeof content === 'object') {
      return (
        <>
          <div className="message-content">Object Message</div>
          <pre>{JSON.stringify(content, null, 2)}</pre>
        </>
      );
    } else {
      return <div className="message-content">{content}</div>;
    }
  };

  // Message templates
  const messageTemplates = [
    { name: 'Ping', content: '{"type": "ping"}' },
    { name: 'Request Data', content: '{"type": "request_data"}' },
    { name: 'Hello', content: '{"type": "message", "content": "Hello, WebSocket!"}' },
    { name: 'Status', content: '{"type": "status_request"}' },
    { name: 'Get Components', content: '{"type": "get_components"}' },
    { name: 'Get Layouts', content: '{"type": "get_layouts"}' },
    { name: 'Get Themes', content: '{"type": "get_themes"}' },
    { name: 'Create Component', content: '{"type": "create_component", "data": {"name": "Test Button", "type": "button", "props": {"text": "Click Me", "variant": "primary"}}}' },
    { name: 'Update Theme', content: '{"type": "update_theme", "theme_id": "default", "data": {"primaryColor": "#FF5733"}}' }
  ];

  // Render error alert for code 1006
  const renderErrorAlert = () => {
    if (!closeInfo || closeInfo.code !== 1006) return null;

    return (
      <Alert
        message="WebSocket Connection Error (Code 1006)"
        description={
          <div>
            <p>
              The WebSocket connection was closed abnormally (code 1006). This typically indicates one of the following issues:
            </p>
            <ul>
              <li>Network connectivity problems</li>
              <li>Server process termination</li>
              <li>Proxy or firewall blocking the connection</li>
              <li>Connection timeout</li>
            </ul>
            <p>
              <strong>Troubleshooting steps:</strong>
            </p>
            <ol>
              <li>Check if the WebSocket server is running</li>
              <li>Verify the WebSocket URL is correct</li>
              <li>Check for network restrictions</li>
              <li>Try using a different WebSocket endpoint</li>
            </ol>
            <div style={{ marginTop: '10px' }}>
              <Button
                type="primary"
                icon={<ReloadOutlined />}
                onClick={resetConnection}
              >
                Reset Connection
              </Button>
            </div>
          </div>
        }
        type="error"
        showIcon
        icon={<BugOutlined />}
        style={{ marginBottom: '16px' }}
      />
    );
  };

  return (
    <WebSocketManagerContainer>
      <Card title="Legacy WebSocket Manager">
        <Alert
          message="Legacy Component"
          description="This is the legacy WebSocket manager. We recommend using the new WebSocket Demo component for better performance and features."
          type="warning"
          showIcon
          style={{ marginBottom: '16px' }}
        />
        
        <Button 
          type="primary" 
          onClick={() => window.location.reload()}
          style={{ marginBottom: '16px' }}
        >
          Switch to New WebSocket Demo
        </Button>
      </Card>
    </WebSocketManagerContainer>
  );
};

export default LegacyWebSocketManager;
