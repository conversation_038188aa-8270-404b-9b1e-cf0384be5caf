/**
 * Backend Status Checker
 * 
 * This utility checks the status of the backend server and provides
 * information about its availability.
 */

import API_ENDPOINTS, { getAllEndpointUrls } from '../config/api';

/**
 * Check if the backend is available
 * @param {Object} options - Options for the check
 * @param {number} options.timeout - Timeout in milliseconds
 * @param {boolean} options.useCache - Whether to use cached results
 * @returns {Promise<Object>} Status information
 */
export async function checkBackendStatus(options = {}) {
  const {
    timeout = 5000,
    useCache = true
  } = options;
  
  // Check if we have cached results and they're recent (less than 1 minute old)
  if (useCache) {
    const cachedStatus = localStorage.getItem('backend_status');
    const cachedTimestamp = localStorage.getItem('backend_status_timestamp');
    
    if (cachedStatus && cachedTimestamp) {
      const cacheAge = Date.now() - parseInt(cachedTimestamp);
      
      // Use cached results if they're less than 1 minute old
      if (cacheAge < 60000) {
        return JSON.parse(cachedStatus);
      }
    }
  }
  
  console.log('Checking backend status...');
  
  // Get all health check endpoints
  const healthEndpoints = getAllEndpointUrls(API_ENDPOINTS.HEALTH);
  const statusEndpoints = getAllEndpointUrls(API_ENDPOINTS.STATUS);
  
  // Combine all endpoints
  const allEndpoints = [...healthEndpoints, ...statusEndpoints];
  
  // Create an AbortController for timeout
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), timeout);
  
  // Check each endpoint
  const results = await Promise.all(
    allEndpoints.map(async (endpoint) => {
      try {
        const response = await fetch(endpoint, {
          method: 'GET',
          headers: {
            'Accept': 'application/json',
            'Cache-Control': 'no-cache'
          },
          signal: controller.signal
        });
        
        return {
          endpoint,
          available: response.ok,
          status: response.status,
          statusText: response.statusText
        };
      } catch (error) {
        return {
          endpoint,
          available: false,
          error: error.name === 'AbortError' ? 'Timeout' : error.message
        };
      }
    })
  );
  
  // Clear timeout
  clearTimeout(timeoutId);
  
  // Check if any endpoint is available
  const availableEndpoints = results.filter(result => result.available);
  const isBackendAvailable = availableEndpoints.length > 0;
  
  // Create status object
  const status = {
    available: isBackendAvailable,
    endpoints: results,
    timestamp: new Date().toISOString()
  };
  
  // Cache the results
  try {
    localStorage.setItem('backend_status', JSON.stringify(status));
    localStorage.setItem('backend_status_timestamp', Date.now().toString());
  } catch (error) {
    console.warn('Failed to cache backend status:', error);
  }
  
  return status;
}

/**
 * Check if the WebSocket server is available
 * @param {Object} options - Options for the check
 * @param {number} options.timeout - Timeout in milliseconds
 * @param {boolean} options.useCache - Whether to use cached results
 * @returns {Promise<Object>} Status information
 */
export async function checkWebSocketStatus(options = {}) {
  const {
    timeout = 5000,
    useCache = true
  } = options;
  
  // Check if we have cached results and they're recent (less than 1 minute old)
  if (useCache) {
    const cachedStatus = localStorage.getItem('websocket_status');
    const cachedTimestamp = localStorage.getItem('websocket_status_timestamp');
    
    if (cachedStatus && cachedTimestamp) {
      const cacheAge = Date.now() - parseInt(cachedTimestamp);
      
      // Use cached results if they're less than 1 minute old
      if (cacheAge < 60000) {
        return JSON.parse(cachedStatus);
      }
    }
  }
  
  console.log('Checking WebSocket status...');
  
  // Get WebSocket URL
  const wsUrl = process.env.REACT_APP_WS_URL || 
                `ws://${window.location.host}/ws/app_builder/`;
  
  // Create a promise that resolves when the WebSocket connects or rejects on error/timeout
  const checkPromise = new Promise((resolve, reject) => {
    try {
      const socket = new WebSocket(wsUrl);
      
      socket.onopen = () => {
        socket.close();
        resolve({
          available: true,
          url: wsUrl
        });
      };
      
      socket.onerror = (error) => {
        socket.close();
        reject(new Error('WebSocket connection error'));
      };
      
      socket.onclose = (event) => {
        if (event.code !== 1000) {
          reject(new Error(`WebSocket closed with code ${event.code}`));
        }
      };
      
      // Set a timeout
      setTimeout(() => {
        if (socket.readyState !== WebSocket.CLOSED) {
          socket.close();
          reject(new Error('WebSocket connection timeout'));
        }
      }, timeout);
    } catch (error) {
      reject(error);
    }
  });
  
  // Wait for the check to complete or timeout
  try {
    const result = await checkPromise;
    
    // Create status object
    const status = {
      available: true,
      url: wsUrl,
      timestamp: new Date().toISOString()
    };
    
    // Cache the results
    try {
      localStorage.setItem('websocket_status', JSON.stringify(status));
      localStorage.setItem('websocket_status_timestamp', Date.now().toString());
    } catch (error) {
      console.warn('Failed to cache WebSocket status:', error);
    }
    
    return status;
  } catch (error) {
    // Create status object
    const status = {
      available: false,
      url: wsUrl,
      error: error.message,
      timestamp: new Date().toISOString()
    };
    
    // Cache the results
    try {
      localStorage.setItem('websocket_status', JSON.stringify(status));
      localStorage.setItem('websocket_status_timestamp', Date.now().toString());
    } catch (cacheError) {
      console.warn('Failed to cache WebSocket status:', cacheError);
    }
    
    return status;
  }
}

export default {
  checkBackendStatus,
  checkWebSocketStatus
};
