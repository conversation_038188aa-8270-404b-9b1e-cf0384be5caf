import React, { createContext, useContext, useState, useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { ThemeProvider as StyledThemeProvider, createGlobalStyle } from 'styled-components';
import { message } from 'antd';
import { loadUserThemePreferences, saveUserThemePreferences, setActiveTheme, toggleAutoApplyTheme } from '../../redux/actions/themeActions';
import {
  lightTheme,
  darkTheme,
  blueTheme,
  highContrastTheme
} from '../../styles/theme';

// Create a context for theme switching
export const EnhancedThemeContext = createContext({
  currentTheme: null,
  setThemeById: () => { },
  availableThemes: [],
  themeMode: 'light',
  toggleThemeMode: () => { },
  setThemeMode: () => { },
});

// Custom hook to use the theme context
export const useEnhancedTheme = () => {
  const context = useContext(EnhancedThemeContext);
  if (!context) {
    throw new Error('useEnhancedTheme must be used within an EnhancedThemeProvider');
  }
  return context;
};

// Define global styles that will be applied based on the current theme
const GlobalStyle = createGlobalStyle`
  :root {
    --primary-color: ${props => {
    if (props.theme?.colorPalette?.primary) return props.theme.colorPalette.primary;
    if (props.theme?.colors?.primary?.main) return props.theme.colors.primary.main;
    if (props.theme?.primaryColor) return props.theme.primaryColor;
    return '#2563EB';
  }};
    --primary-light: ${props => {
    if (props.theme?.colorPalette?.primaryLight) return props.theme.colorPalette.primaryLight;
    if (props.theme?.colors?.primary?.light) return props.theme.colors.primary.light;
    return '#DBEAFE';
  }};
    --primary-dark: ${props => {
    if (props.theme?.colorPalette?.primaryDark) return props.theme.colorPalette.primaryDark;
    if (props.theme?.colors?.primary?.dark) return props.theme.colors.primary.dark;
    return '#1E40AF';
  }};
    --secondary-color: ${props => {
    if (props.theme?.colorPalette?.info) return props.theme.colorPalette.info;
    if (props.theme?.colors?.secondary?.main) return props.theme.colors.secondary.main;
    if (props.theme?.secondaryColor) return props.theme.secondaryColor;
    return '#10B981';
  }};
    --background-color: ${props => {
    if (props.theme?.colorPalette?.background) return props.theme.colorPalette.background;
    if (props.theme?.colors?.background?.default) return props.theme.colors.background.default;
    if (props.theme?.backgroundColor) return props.theme.backgroundColor;
    return '#FFFFFF';
  }};
    --background-secondary: ${props => {
    if (props.theme?.colorPalette?.backgroundSecondary) return props.theme.colorPalette.backgroundSecondary;
    if (props.theme?.colors?.background?.paper) return props.theme.colors.background.paper;
    return '#F9FAFB';
  }};
    --text-color: ${props => {
    if (props.theme?.colorPalette?.textPrimary) return props.theme.colorPalette.textPrimary;
    if (props.theme?.colors?.text?.primary) return props.theme.colors.text.primary;
    if (props.theme?.textColor) return props.theme.textColor;
    return '#111827';
  }};
    --text-secondary: ${props => {
    if (props.theme?.colorPalette?.textSecondary) return props.theme.colorPalette.textSecondary;
    if (props.theme?.colors?.text?.secondary) return props.theme.colors.text.secondary;
    return '#4B5563';
  }};
    --border-color: ${props => {
    if (props.theme?.colorPalette?.border) return props.theme.colorPalette.border;
    if (props.theme?.colors?.neutral?.[300]) return props.theme.colors.neutral[300];
    return '#D1D5DB';
  }};
    --success-color: ${props => {
    if (props.theme?.colorPalette?.success) return props.theme.colorPalette.success;
    if (props.theme?.colors?.success?.main) return props.theme.colors.success.main;
    return '#10B981';
  }};
    --warning-color: ${props => {
    if (props.theme?.colorPalette?.warning) return props.theme.colorPalette.warning;
    if (props.theme?.colors?.warning?.main) return props.theme.colors.warning.main;
    return '#FBBF24';
  }};
    --error-color: ${props => {
    if (props.theme?.colorPalette?.error) return props.theme.colorPalette.error;
    if (props.theme?.colors?.error?.main) return props.theme.colors.error.main;
    return '#DC2626';
  }};
    --font-family: ${props => props.fontFamily || '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif'};

    --spacing-xs: ${props => props.theme.spacing.xs}px;
    --spacing-sm: ${props => props.theme.spacing.sm}px;
    --spacing-md: ${props => props.theme.spacing.md}px;
    --spacing-lg: ${props => props.theme.spacing.lg}px;
    --spacing-xl: ${props => props.theme.spacing.xl}px;

    --border-radius-sm: ${props => props.theme.borderRadius.sm};
    --border-radius-md: ${props => props.theme.borderRadius.md};
    --border-radius-lg: ${props => props.theme.borderRadius.lg};
  }

  body {
    font-family: var(--font-family);
    background-color: var(--background-color);
    color: var(--text-color);
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    transition: background-color 0.3s ease, color 0.3s ease;
  }

  *, *:before, *:after {
    box-sizing: inherit;
  }

  h1, h2, h3, h4, h5, h6 {
    margin-top: 0;
    font-weight: ${props => props.theme.fontWeights.semibold};
    color: var(--text-color);
    transition: color 0.3s ease;
  }

  a {
    color: var(--primary-color);
    text-decoration: none;
    transition: color 0.3s ease;

    &:hover {
      text-decoration: underline;
    }
  }

  button {
    font-family: var(--font-family);
  }

  .ant-layout {
    background-color: var(--background-color);
    transition: background-color 0.3s ease;
  }

  .ant-layout-header {
    background-color: var(--background-secondary);
    transition: background-color 0.3s ease;
  }

  .ant-layout-footer {
    background-color: var(--background-secondary);
    transition: background-color 0.3s ease;
  }

  .ant-layout-content {
    background-color: var(--background-color);
    transition: background-color 0.3s ease;
  }

  .ant-card {
    background-color: var(--background-secondary);
    transition: background-color 0.3s ease;
  }

  .ant-menu {
    background-color: transparent;
    color: var(--text-color);
    transition: color 0.3s ease;
  }

  .ant-btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
  }

  .ant-btn-primary:hover {
    background-color: var(--primary-dark);
    border-color: var(--primary-dark);
  }
`;

const EnhancedThemeProvider = ({ children }) => {
  // Default values if Redux is not available
  const defaultThemes = [];
  const defaultActiveThemeId = 'default';
  const defaultUserPreferences = { savedTheme: null, autoApplyTheme: true };

  // Check if we're in a Redux context
  const isReduxAvailable = typeof useSelector === 'function' && typeof useDispatch === 'function';

  // Safely access Redux hooks with error handling
  let dispatch = null;
  let themes = defaultThemes;
  let activeThemeId = defaultActiveThemeId;
  let userPreferences = defaultUserPreferences;

  if (isReduxAvailable) {
    try {
      dispatch = useDispatch();

      // Get themes from Redux store with fallback values
      themes = useSelector(state => {
        if (!state || !state.themes) {
          console.warn('Redux state or themes slice not found, using fallback values');
          return defaultThemes;
        }
        return state.themes.themes || defaultThemes;
      });

      activeThemeId = useSelector(state => {
        if (!state || !state.themes) {
          return defaultActiveThemeId;
        }
        return state.themes.activeTheme || defaultActiveThemeId;
      });

      userPreferences = useSelector(state => {
        if (!state || !state.themes || !state.themes.userPreferences) {
          return defaultUserPreferences;
        }
        return state.themes.userPreferences;
      });
    } catch (error) {
      console.error('Error accessing Redux context in EnhancedThemeProvider:', error);
      // Continue with default values
    }
  } else {
    console.warn('Redux context not available in EnhancedThemeProvider, using default values');
  }

  // Load user preferences on component mount
  useEffect(() => {
    if (dispatch) {
      try {
        dispatch(loadUserThemePreferences());
      } catch (error) {
        console.error('Error loading user theme preferences:', error);
      }
    }
  }, [dispatch]);

  // Available theme modes
  const availableThemeModes = ['light', 'dark', 'blue', 'high-contrast'];

  // Get the initial theme mode from localStorage or default to light
  const [themeMode, setThemeMode] = useState(() => {
    const savedThemeMode = localStorage.getItem('themeMode');
    return availableThemeModes.includes(savedThemeMode) ? savedThemeMode : 'light';
  });

  // Get the current theme from Redux or use default
  const getCurrentTheme = () => {
    const defaultTheme = {
      id: 'default',
      name: 'Default Theme',
      primaryColor: '#2563EB',
      secondaryColor: '#10B981',
      backgroundColor: '#FFFFFF',
      textColor: '#111827',
      fontFamily: 'Inter, sans-serif'
    };

    // Ensure themes is an array before using find
    const safeThemes = Array.isArray(themes) ? themes : [];
    return safeThemes.find(theme => theme.id === activeThemeId) || defaultTheme;
  };

  const [currentTheme, setCurrentTheme] = useState(getCurrentTheme());

  // Update current theme when activeThemeId changes
  useEffect(() => {
    setCurrentTheme(getCurrentTheme());
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [activeThemeId]);

  // Set theme by ID
  const setThemeById = (themeId) => {
    // Ensure themes is an array before using find
    const safeThemes = Array.isArray(themes) ? themes : [];
    const theme = safeThemes.find(t => t.id === themeId);
    if (theme) {
      // Dispatch action to update active theme in Redux if dispatch is available
      if (dispatch && isReduxAvailable) {
        try {
          // Use the action creator instead of dispatching directly
          dispatch(setActiveTheme(themeId));

          // Save user preferences to localStorage
          dispatch(saveUserThemePreferences());
        } catch (error) {
          console.error('Error dispatching theme actions:', error);
          // Fall back to local state if Redux dispatch fails
          setCurrentTheme(theme);
          // Save to localStorage as fallback
          try {
            localStorage.setItem('theme', JSON.stringify({ id: themeId }));
          } catch (storageError) {
            console.error('Error saving theme to localStorage:', storageError);
          }
        }
      } else {
        console.warn('Redux dispatch function not available, using local state and localStorage');
        // Set the theme locally
        setCurrentTheme(theme);
        // Save to localStorage as fallback
        try {
          localStorage.setItem('theme', JSON.stringify({ id: themeId }));
        } catch (storageError) {
          console.error('Error saving theme to localStorage:', storageError);
        }
      }

      // Show success message
      message.success(`Theme "${theme.name}" applied successfully`);

      // Notify service worker
      if ('serviceWorker' in navigator && navigator.serviceWorker.controller) {
        navigator.serviceWorker.controller.postMessage({
          type: 'THEME_UPDATED',
          theme
        });
      }
    }
  };

  // Toggle auto-apply theme setting
  const toggleAutoApply = () => {
    if (dispatch && isReduxAvailable) {
      try {
        dispatch(toggleAutoApplyTheme());
        dispatch(saveUserThemePreferences());

        // Show success message
        const newState = !userPreferences.autoApplyTheme;
        message.success(`Auto-apply theme ${newState ? 'enabled' : 'disabled'}`);
      } catch (error) {
        console.error('Error toggling auto-apply theme:', error);
        // Fall back to local storage
        handleLocalAutoApplyToggle();
      }
    } else {
      console.warn('Redux dispatch function not available, using localStorage for auto-apply theme');
      handleLocalAutoApplyToggle();
    }
  };

  // Helper function to toggle auto-apply using localStorage
  const handleLocalAutoApplyToggle = () => {
    try {
      const currentSetting = localStorage.getItem('autoApplyTheme') === 'true';
      const newSetting = !currentSetting;
      localStorage.setItem('autoApplyTheme', String(newSetting));

      // Update local state (this won't trigger a re-render, but will be used for future operations)
      userPreferences = {
        ...userPreferences,
        autoApplyTheme: newSetting
      };

      // Show success message
      message.success(`Auto-apply theme ${newSetting ? 'enabled' : 'disabled'}`);
    } catch (storageError) {
      console.error('Error saving auto-apply setting to localStorage:', storageError);
    }
  };

  // Toggle between theme modes (light, dark, blue, high-contrast)
  const toggleThemeMode = () => {
    const currentIndex = availableThemeModes.indexOf(themeMode);
    const nextIndex = (currentIndex + 1) % availableThemeModes.length;
    const newMode = availableThemeModes[nextIndex];
    setThemeMode(newMode);
    localStorage.setItem('themeMode', newMode);

    // Show notification
    message.info(`Switched to ${newMode} mode`);
  };

  // Set specific theme mode
  const handleSetThemeMode = (mode) => {
    if (availableThemeModes.includes(mode)) {
      setThemeMode(mode);
      localStorage.setItem('themeMode', mode);

      // Show notification
      message.info(`Switched to ${mode} mode`);
    }
  };

  // Get the base theme based on theme mode
  const getBaseTheme = (mode) => {
    switch (mode) {
      case 'dark':
        return darkTheme;
      case 'blue':
        return blueTheme;
      case 'high-contrast':
        return highContrastTheme;
      case 'light':
      default:
        return lightTheme;
    }
  };

  // Combine base theme with custom theme properties
  const getTheme = () => {
    const baseTheme = getBaseTheme(themeMode);

    // If we have a custom theme, override some properties
    if (currentTheme) {
      return {
        ...baseTheme,
        customTheme: currentTheme,
        fontFamily: currentTheme.fontFamily,
        colorPalette: {
          ...baseTheme.colorPalette,
          primary: currentTheme.primaryColor,
          secondary: currentTheme.secondaryColor,
          background: currentTheme.backgroundColor,
          textPrimary: currentTheme.textColor
        }
      };
    }

    return baseTheme;
  };

  // Apply theme to document body
  useEffect(() => {
    document.body.dataset.theme = themeMode;
    document.body.dataset.customTheme = currentTheme?.id || 'default';
  }, [themeMode, currentTheme]);

  // Listen for theme updates from service worker
  useEffect(() => {
    // Create a memoized version of getCurrentTheme that doesn't depend on the themes array
    const getThemeForServiceWorker = () => {
      const defaultTheme = {
        id: 'default',
        name: 'Default Theme',
        primaryColor: '#2563EB',
        secondaryColor: '#10B981',
        backgroundColor: '#FFFFFF',
        textColor: '#111827',
        fontFamily: 'Inter, sans-serif'
      };

      // Ensure themes is an array before using find
      const safeThemes = Array.isArray(themes) ? themes : [];
      return safeThemes.find(theme => theme.id === activeThemeId) || defaultTheme;
    };

    const handleServiceWorkerMessage = (event) => {
      if (event.data && event.data.type === 'THEME_UPDATED') {
        // Refresh the current theme
        setCurrentTheme(getThemeForServiceWorker());
      }
    };

    if ('serviceWorker' in navigator) {
      navigator.serviceWorker.addEventListener('message', handleServiceWorkerMessage);
    }

    return () => {
      if ('serviceWorker' in navigator) {
        navigator.serviceWorker.removeEventListener('message', handleServiceWorkerMessage);
      }
    };
    // We intentionally don't include dependencies here to avoid re-registering the listener
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <EnhancedThemeContext.Provider value={{
      currentTheme,
      setThemeById,
      availableThemes: themes,
      themeMode,
      toggleThemeMode,
      setThemeMode: handleSetThemeMode,
      availableThemeModes,
      userPreferences,
      toggleAutoApply
    }}>
      <StyledThemeProvider theme={getTheme()}>
        <GlobalStyle fontFamily={currentTheme?.fontFamily} />
        {children}
      </StyledThemeProvider>
    </EnhancedThemeContext.Provider>
  );
};

export default EnhancedThemeProvider;
