from django.contrib import admin
from django.urls import path, include, re_path
from django.views.generic import TemplateView
from .health import health_check

urlpatterns = [
    path('admin/', admin.site.urls),
    path('api/', include('my_app.urls')),  # Include app URLs
    path('health/', health_check, name='health_check'),  # Health check endpoint
    # Serve the frontend only for non-API routes
    re_path(r'^(?!api/)(?!admin/)(?!health/).*$', TemplateView.as_view(template_name="index.html"), name='home'),
]
