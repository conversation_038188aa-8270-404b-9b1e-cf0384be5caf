# PowerShell script to check Docker container status

Write-Host "Checking Docker container status..." -ForegroundColor Cyan

# Check if Dock<PERSON> is running
try {
    $dockerInfo = docker info 2>&1
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ Docker is not running. Please start Docker Desktop." -ForegroundColor Red
        exit 1
    }
    Write-Host "✅ Docker is running" -ForegroundColor Green
} catch {
    Write-Host "❌ Error checking Docker: $_" -ForegroundColor Red
    Write-Host "Please make sure Docker is installed and running." -ForegroundColor Yellow
    exit 1
}

# List all containers
Write-Host "`nListing all containers:" -ForegroundColor Yellow
docker ps -a

# Check container logs
Write-Host "`nChecking container logs:" -ForegroundColor Yellow

$containers = docker-compose ps --services 2>&1
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Error getting container list: $containers" -ForegroundColor Red
} else {
    foreach ($container in $containers) {
        if ($container.Trim()) {
            Write-Host "`nLogs for $container:" -ForegroundColor Cyan
            docker-compose logs --tail=20 $container
        }
    }
}

# Check container health
Write-Host "`nChecking container health:" -ForegroundColor Yellow
docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"

# Check Docker networks
Write-Host "`nChecking Docker networks:" -ForegroundColor Yellow
docker network ls

# Check Docker volumes
Write-Host "`nChecking Docker volumes:" -ForegroundColor Yellow
docker volume ls

Write-Host "`nContainer check complete!" -ForegroundColor Cyan