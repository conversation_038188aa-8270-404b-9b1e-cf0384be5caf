import React, { useState, useEffect } from 'react';
import { Layout, <PERSON>u, <PERSON><PERSON>, Drawer, Avatar, Dropdown } from 'antd';
import {
  MenuOutlined,
  HomeOutlined,
  AppstoreOutlined,
  WifiOutlined,
  CodeOutlined,
  FileTextOutlined,
  SettingOutlined,
  UserOutlined,
  LoginOutlined,
  LogoutOutlined,
  BarsOutlined,
} from '@ant-design/icons';
import { Link, useLocation } from 'react-router-dom';
import styled from 'styled-components';
import { useTranslation } from 'react-i18next';
import { useAuth } from '../../contexts/AuthContext';
import ThemeSwitcher from '../ThemeSwitcher';
import LanguageSwitcher from '../LanguageSwitcher';
import * as S from '../../styles/components';

const { Header, Content, Footer, Sider } = Layout;

// Styled components
const StyledLayout = styled(Layout)`
  min-height: 100vh;
`;

const StyledHeader = styled(Header)`
  background-color: ${props => {
    if (props.theme?.colorPalette?.primary) return props.theme.colorPalette.primary;
    if (props.theme?.colors?.primary?.main) return props.theme.colors.primary.main;
    if (props.theme?.primaryColor) return props.theme.primaryColor;
    return '#2563EB';
  }};
  padding: 0 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: fixed;
  z-index: 1000;
  width: 100%;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
`;

const Logo = styled.div`
  color: white;
  font-size: 18px;
  font-weight: bold;
  display: flex;
  align-items: center;
  
  @media (max-width: 576px) {
    font-size: 16px;
  }
`;

const StyledContent = styled(Content)`
  margin-top: 64px;
  padding: 24px;
  background-color: ${props => {
    if (props.theme?.colorPalette?.background) return props.theme.colorPalette.background;
    if (props.theme?.colors?.background?.default) return props.theme.colors.background.default;
    if (props.theme?.backgroundColor) return props.theme.backgroundColor;
    return '#FFFFFF';
  }};

  @media (max-width: 576px) {
    padding: 16px;
  }
`;

const StyledFooter = styled(Footer)`
  text-align: center;
  background-color: ${props => {
    if (props.theme?.colorPalette?.backgroundSecondary) return props.theme.colorPalette.backgroundSecondary;
    if (props.theme?.colors?.background?.paper) return props.theme.colors.background.paper;
    return '#F9FAFB';
  }};
  padding: 16px;
`;

const MobileMenuButton = styled(Button)`
  margin-right: 16px;
  display: none;
  
  @media (max-width: 768px) {
    display: block;
  }
`;

const DesktopMenu = styled(Menu)`
  @media (max-width: 768px) {
    display: none;
  }
`;

const HeaderRight = styled.div`
  display: flex;
  align-items: center;
`;

const UserDropdown = styled.div`
  margin-left: 16px;
  cursor: pointer;
`;

const UserAvatar = styled(Avatar)`
  background-color: ${props => {
    if (props.theme?.colorPalette?.secondary) return props.theme.colorPalette.secondary;
    if (props.theme?.colors?.secondary?.main) return props.theme.colors.secondary.main;
    if (props.theme?.secondaryColor) return props.theme.secondaryColor;
    return '#10B981';
  }};
`;

/**
 * ResponsiveLayout component
 * Provides a responsive layout for the application
 * 
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - Child components to render
 * @returns {React.ReactElement} Responsive layout
 */
const ResponsiveLayout = ({ children }) => {
  const { t } = useTranslation();
  const location = useLocation();
  const { user, isAuthenticated, logout } = useAuth();
  const [mobileMenuVisible, setMobileMenuVisible] = useState(false);
  const [selectedKeys, setSelectedKeys] = useState([]);

  // Update selected keys when location changes
  useEffect(() => {
    const pathname = location.pathname;
    setSelectedKeys([pathname]);
  }, [location]);

  // Toggle mobile menu
  const toggleMobileMenu = () => {
    setMobileMenuVisible(!mobileMenuVisible);
  };

  // Close mobile menu
  const closeMobileMenu = () => {
    setMobileMenuVisible(false);
  };

  // Handle logout
  const handleLogout = async () => {
    await logout();
  };

  // Navigation items
  const navItems = [
    {
      key: '/',
      icon: <HomeOutlined />,
      label: t('navigation.home'),
    },
    {
      key: '/app-builder',
      icon: <AppstoreOutlined />,
      label: t('navigation.appBuilder'),
    },
    {
      key: '/websocket',
      icon: <WifiOutlined />,
      label: t('navigation.webSocket'),
    },
    {
      key: '/bootstrap-test',
      icon: <CodeOutlined />,
      label: t('navigation.bootstrapTest'),
    },
    {
      key: '/style-guide',
      icon: <BarsOutlined />,
      label: t('navigation.styleGuide'),
    },
    {
      key: '/documentation',
      icon: <FileTextOutlined />,
      label: t('navigation.documentation'),
    },
    {
      key: '/preferences',
      icon: <SettingOutlined />,
      label: t('navigation.preferences'),
    },
  ];

  // User dropdown menu items
  const userMenuItems = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: <Link to="/profile">{t('profile.title')}</Link>,
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: <span onClick={handleLogout}>{t('auth.logout')}</span>,
    },
  ];

  return (
    <StyledLayout>
      <StyledHeader>
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <MobileMenuButton
            type="text"
            icon={<MenuOutlined />}
            onClick={toggleMobileMenu}
            aria-label="Toggle menu"
          />
          <Logo>
            <Link to="/" style={{ color: 'white' }}>
              {t('app.title')}
            </Link>
          </Logo>
        </div>

        <DesktopMenu
          theme="dark"
          mode="horizontal"
          selectedKeys={selectedKeys}
          items={navItems.map(item => ({
            ...item,
            label: <Link to={item.key}>{item.label}</Link>,
          }))}
        />

        <HeaderRight>
          <LanguageSwitcher />
          <ThemeSwitcher type="icon" />

          {isAuthenticated ? (
            <Dropdown
              menu={{
                items: userMenuItems,
              }}
              placement="bottomRight"
            >
              <UserDropdown>
                <UserAvatar icon={<UserOutlined />} src={user?.avatar} />
              </UserDropdown>
            </Dropdown>
          ) : (
            <Link to="/login">
              <Button type="text" icon={<LoginOutlined />} style={{ color: 'white' }}>
                {t('auth.login.title')}
              </Button>
            </Link>
          )}
        </HeaderRight>
      </StyledHeader>

      <Drawer
        title={t('app.title')}
        placement="left"
        onClose={closeMobileMenu}
        open={mobileMenuVisible}
        bodyStyle={{ padding: 0 }}
      >
        <Menu
          mode="inline"
          selectedKeys={selectedKeys}
          items={navItems.map(item => ({
            ...item,
            label: <Link to={item.key} onClick={closeMobileMenu}>{item.label}</Link>,
          }))}
        />

        {isAuthenticated ? (
          <Menu
            mode="inline"
            items={[
              {
                key: 'profile',
                icon: <UserOutlined />,
                label: <Link to="/profile" onClick={closeMobileMenu}>{t('profile.title')}</Link>,
              },
              {
                key: 'logout',
                icon: <LogoutOutlined />,
                label: <span onClick={() => { handleLogout(); closeMobileMenu(); }}>{t('auth.logout')}</span>,
              },
            ]}
          />
        ) : (
          <Menu
            mode="inline"
            items={[
              {
                key: 'login',
                icon: <LoginOutlined />,
                label: <Link to="/login" onClick={closeMobileMenu}>{t('auth.login.title')}</Link>,
              },
              {
                key: 'register',
                icon: <UserOutlined />,
                label: <Link to="/register" onClick={closeMobileMenu}>{t('auth.register.title')}</Link>,
              },
            ]}
          />
        )}
      </Drawer>

      <StyledContent>
        {children}
      </StyledContent>

      <StyledFooter>
        {t('app.title')} ©{new Date().getFullYear()} - {t('app.description')}
      </StyledFooter>
    </StyledLayout>
  );
};

export default ResponsiveLayout;
