# Frequently Asked Questions (FAQ)

## General Questions

### What is App Builder 201?
App Builder 201 is a modern, full-stack application builder that allows you to create applications visually using a drag-and-drop interface. It features real-time collaboration, a comprehensive component library, and the ability to export your creations as production-ready code.

### Who is App Builder 201 for?
- **Developers**: Rapid prototyping and application development
- **Designers**: Visual application design without coding
- **Product Managers**: Quick mockups and proof of concepts
- **Teams**: Collaborative application development
- **Students**: Learning application development concepts

### What types of applications can I build?
- Web applications (React-based)
- Landing pages and marketing sites
- Dashboard and admin interfaces
- E-commerce applications
- Portfolio and blog sites
- Mobile-responsive applications

### Is App Builder 201 free?
Please check our pricing page for current pricing information. We offer various plans including free tiers for personal use and paid plans for teams and enterprises.

## Technical Questions

### What technologies does App Builder 201 use?
- **Frontend**: React 18, Ant Design, WebSocket client
- **Backend**: Django 4.2, Django Channels, PostgreSQL
- **Infrastructure**: <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Redis
- **Deployment**: Docker Compose, Let's Encrypt SSL

### What browsers are supported?
- Chrome 90+ (recommended)
- Firefox 88+
- Safari 14+
- Edge 90+

### Can I use App Builder 201 offline?
Yes! App Builder 201 is a Progressive Web App (PWA) with offline capabilities:
- Work on your projects without internet connection
- Automatic sync when connection is restored
- Cached assets for faster loading

### What export formats are available?
- **React Components**: Clean, production-ready React code
- **HTML/CSS**: Static website files
- **JSON**: Project data for backup or migration
- **Mobile**: React Native components (coming soon)

## Usage Questions

### How do I get started?
1. Access the application at your provided URL
2. Sign in with your credentials
3. Create a new project or choose a template
4. Start building with the drag-and-drop interface
5. Export your application when ready

### Can multiple people work on the same project?
Yes! App Builder 201 supports real-time collaboration:
- Multiple users can edit simultaneously
- See live cursors of other users
- Real-time updates and synchronization
- Comment and feedback system

### How do I save my work?
- **Auto-save**: Your work is automatically saved as you build
- **Manual save**: Use Ctrl+S (Cmd+S on Mac) to save manually
- **Version history**: Access previous versions of your project
- **Export**: Download your project files for backup

### Can I import existing code or designs?
- **Import projects**: Upload previously exported JSON files
- **Copy components**: Copy and paste components between projects
- **Custom components**: Add your own React components
- **Design imports**: Import designs from Figma (coming soon)

## Collaboration Questions

### How many people can work on a project?
The number of collaborators depends on your plan:
- **Free**: Up to 2 collaborators
- **Pro**: Up to 10 collaborators
- **Team**: Up to 50 collaborators
- **Enterprise**: Unlimited collaborators

### How do permissions work?
- **Owner**: Full access to project settings and collaboration
- **Editor**: Can edit the project and invite others
- **Viewer**: Can view the project but not make changes
- **Commenter**: Can add comments but not edit

### Can I see who made what changes?
Yes! App Builder 201 includes comprehensive change tracking:
- View edit history with timestamps
- See who made each change
- Revert to previous versions
- Comment on specific changes

## Deployment Questions

### How do I deploy my application?
1. **Export your code**: Download the generated files
2. **Choose hosting**: Use any web hosting service
3. **Upload files**: Deploy to your hosting platform
4. **Configure domain**: Point your domain to the hosting service

### What hosting services are recommended?
- **Static sites**: Netlify, Vercel, GitHub Pages
- **Full applications**: AWS, Google Cloud, DigitalOcean
- **Managed hosting**: Heroku, Railway, Render
- **Self-hosted**: Your own servers with Docker

### Can I customize the generated code?
Yes! The exported code is clean and customizable:
- Standard React components
- Well-structured CSS
- Commented code for easy understanding
- No vendor lock-in

### How do I set up a custom domain?
1. Export and deploy your application
2. Configure DNS settings with your domain provider
3. Point your domain to your hosting service
4. Set up SSL certificates (usually automatic with modern hosts)

## Troubleshooting Questions

### The application is loading slowly. What can I do?
- **Clear browser cache**: Hard refresh with Ctrl+Shift+R
- **Check internet connection**: Ensure stable connectivity
- **Close other tabs**: Free up browser resources
- **Use Chrome**: Chrome generally has the best performance
- **Contact support**: If issues persist

### I can't see my changes. What's wrong?
- **Check auto-save**: Ensure auto-save is enabled
- **Manual save**: Try saving manually with Ctrl+S
- **Refresh page**: Reload the page to sync changes
- **Check collaboration**: Another user might have made conflicting changes

### My exported code doesn't work. How do I fix it?
- **Check dependencies**: Ensure all required packages are installed
- **Review console errors**: Check browser console for error messages
- **Validate structure**: Ensure proper component nesting
- **Contact support**: Send us the exported code for review

### I lost my work. Can it be recovered?
- **Check version history**: Access previous versions in the project settings
- **Auto-save recovery**: The system may have auto-saved your work
- **Browser storage**: Check if data is cached in your browser
- **Contact support**: We may be able to help recover recent work

## Account Questions

### How do I reset my password?
1. Go to the login page
2. Click "Forgot Password"
3. Enter your email address
4. Check your email for reset instructions
5. Follow the link to create a new password

### How do I change my email address?
1. Go to Account Settings
2. Click "Change Email"
3. Enter your new email address
4. Verify the new email address
5. Confirm the change

### How do I delete my account?
1. Go to Account Settings
2. Scroll to "Danger Zone"
3. Click "Delete Account"
4. Confirm deletion (this cannot be undone)
5. All your projects will be permanently deleted

### Can I transfer projects to another account?
Yes, project owners can:
- Transfer ownership to another user
- Export projects and share the files
- Invite others as collaborators
- Contact support for bulk transfers

## Billing Questions

### How does billing work?
- **Monthly/Annual**: Choose your preferred billing cycle
- **Per seat**: Pay for the number of users
- **Usage-based**: Some features may have usage limits
- **Automatic**: Billing is processed automatically

### Can I change my plan?
Yes! You can upgrade or downgrade your plan at any time:
- Changes take effect immediately for upgrades
- Downgrades take effect at the next billing cycle
- Prorated billing for plan changes

### What payment methods are accepted?
- Credit cards (Visa, MasterCard, American Express)
- PayPal
- Bank transfers (for enterprise plans)
- Cryptocurrency (Bitcoin, Ethereum)

### Do you offer refunds?
- **30-day money-back guarantee** for new subscriptions
- **Prorated refunds** for downgrades
- **No refunds** for usage-based charges
- Contact support for special circumstances

## Security Questions

### Is my data secure?
Yes! We implement comprehensive security measures:
- **Encryption**: All data encrypted in transit and at rest
- **Authentication**: Secure login with optional 2FA
- **Access controls**: Role-based permissions
- **Regular audits**: Security audits and penetration testing

### Where is my data stored?
- **Primary**: Secure cloud infrastructure (AWS/Google Cloud)
- **Backups**: Multiple geographic locations
- **Compliance**: SOC 2, GDPR, and other standards
- **Data residency**: Choose your preferred region

### Can I export my data?
Yes! You have full control over your data:
- Export individual projects
- Bulk export all projects
- Download user data
- API access for automated exports

### How do you handle GDPR compliance?
- **Data minimization**: We only collect necessary data
- **User rights**: Access, modify, and delete your data
- **Consent**: Clear consent for data processing
- **Data protection officer**: Available for GDPR inquiries

## Still Have Questions?

### Contact Support
- **Email**: <EMAIL>
- **Live Chat**: Available during business hours
- **Community Forum**: Get help from other users
- **Documentation**: Comprehensive guides and tutorials

### Response Times
- **Free users**: 48-72 hours
- **Paid users**: 24 hours
- **Enterprise**: 4-8 hours
- **Critical issues**: 1-2 hours

---

Can't find what you're looking for? [Contact our support team](mailto:<EMAIL>) or check our [documentation](../README.md).
