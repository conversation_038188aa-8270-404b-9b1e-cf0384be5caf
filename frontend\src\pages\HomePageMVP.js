import React from 'react';
import { Link } from 'react-router-dom';
import { Card, Button, Row, Col, Typography, Space, Alert } from 'antd';
import { 
  AppstoreOutlined, ToolOutlined, GlobalOutlined, 
  RocketOutlined, ThunderboltOutlined, StarOutlined 
} from '@ant-design/icons';

const { Title, Paragraph } = Typography;

/**
 * HomePageMVP component
 * MVP-focused landing page for the application
 */
const HomePageMVP = () => {
  return (
    <div style={{ padding: '40px 20px', maxWidth: '1200px', margin: '0 auto' }}>
      {/* Hero Section */}
      <div style={{ textAlign: 'center', marginBottom: '60px' }}>
        <Title level={1} style={{ 
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          WebkitBackgroundClip: 'text',
          WebkitTextFillColor: 'transparent',
          marginBottom: '20px'
        }}>
          App Builder 201 - MVP
        </Title>
        <Paragraph style={{ fontSize: '20px', color: '#666', maxWidth: '600px', margin: '0 auto' }}>
          Build your application with minimal setup. Start with our MVP version and create amazing apps in minutes.
        </Paragraph>
        
        <Space size="large" style={{ marginTop: '30px' }}>
          <Link to="/mvp">
            <Button 
              type="primary" 
              size="large" 
              icon={<RocketOutlined />}
              style={{ 
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                border: 'none',
                height: '50px',
                padding: '0 30px',
                fontSize: '16px'
              }}
            >
              Start Building Now
            </Button>
          </Link>
          <Link to="/app-builder">
            <Button size="large" style={{ height: '50px', padding: '0 30px' }}>
              Full Version
            </Button>
          </Link>
        </Space>
      </div>

      {/* MVP Alert */}
      <Alert
        message="🚀 MVP Version Available"
        description="Try our streamlined MVP version for quick app prototyping. Perfect for getting started with core features."
        type="info"
        showIcon
        style={{ marginBottom: '40px' }}
        action={
          <Link to="/mvp">
            <Button size="small" type="primary">
              Try MVP
            </Button>
          </Link>
        }
      />

      {/* Main Features */}
      <Row gutter={[32, 32]} justify="center">
        <Col xs={24} sm={12} lg={8}>
          <Card
            hoverable
            style={{ textAlign: 'center', height: '100%' }}
            cover={
              <div style={{ padding: '40px', backgroundColor: '#f0f2f5' }}>
                <ThunderboltOutlined style={{ fontSize: '48px', color: '#1890ff' }} />
              </div>
            }
          >
            <Title level={3}>MVP Builder</Title>
            <Paragraph>
              Quick and simple app builder with essential features. Perfect for rapid prototyping and MVP development.
            </Paragraph>
            <Link to="/mvp">
              <Button type="primary" size="large">
                Start MVP
              </Button>
            </Link>
          </Card>
        </Col>

        <Col xs={24} sm={12} lg={8}>
          <Card
            hoverable
            style={{ textAlign: 'center', height: '100%' }}
            cover={
              <div style={{ padding: '40px', backgroundColor: '#f0f2f5' }}>
                <AppstoreOutlined style={{ fontSize: '48px', color: '#52c41a' }} />
              </div>
            }
          >
            <Title level={3}>Full App Builder</Title>
            <Paragraph>
              Complete app builder with advanced features, themes, collaboration, and comprehensive tooling.
            </Paragraph>
            <Link to="/app-builder">
              <Button type="primary" size="large">
                Full Builder
              </Button>
            </Link>
          </Card>
        </Col>

        <Col xs={24} sm={12} lg={8}>
          <Card
            hoverable
            style={{ textAlign: 'center', height: '100%' }}
            cover={
              <div style={{ padding: '40px', backgroundColor: '#f0f2f5' }}>
                <ToolOutlined style={{ fontSize: '48px', color: '#fa8c16' }} />
              </div>
            }
          >
            <Title level={3}>WebSocket Testing</Title>
            <Paragraph>
              Test real-time communication features and WebSocket connections for your applications.
            </Paragraph>
            <Link to="/websocket">
              <Button type="primary" size="large">
                Test WebSocket
              </Button>
            </Link>
          </Card>
        </Col>
      </Row>

      {/* MVP Features */}
      <div style={{ textAlign: 'center', marginTop: '60px' }}>
        <Title level={2}>MVP Features</Title>
        <Paragraph style={{ fontSize: '16px', color: '#666', marginBottom: '40px' }}>
          Everything you need to build your first app
        </Paragraph>
        
        <Row gutter={[24, 24]} style={{ marginTop: '40px' }}>
          <Col xs={24} md={6}>
            <div style={{ textAlign: 'center' }}>
              <StarOutlined style={{ fontSize: '32px', color: '#1890ff', marginBottom: '16px' }} />
              <Title level={4}>Component Creation</Title>
              <Paragraph>
                Add buttons, text, inputs, images, cards, and lists with simple configuration.
              </Paragraph>
            </div>
          </Col>
          <Col xs={24} md={6}>
            <div style={{ textAlign: 'center' }}>
              <StarOutlined style={{ fontSize: '32px', color: '#52c41a', marginBottom: '16px' }} />
              <Title level={4}>Layout Design</Title>
              <Paragraph>
                Organize components using Grid, Flex, and Stack layouts with responsive options.
              </Paragraph>
            </div>
          </Col>
          <Col xs={24} md={6}>
            <div style={{ textAlign: 'center' }}>
              <StarOutlined style={{ fontSize: '32px', color: '#fa8c16', marginBottom: '16px' }} />
              <Title level={4}>Real-time Updates</Title>
              <Paragraph>
                See changes instantly with WebSocket-powered live updates and collaboration.
              </Paragraph>
            </div>
          </Col>
          <Col xs={24} md={6}>
            <div style={{ textAlign: 'center' }}>
              <StarOutlined style={{ fontSize: '32px', color: '#722ed1', marginBottom: '16px' }} />
              <Title level={4}>Export & Save</Title>
              <Paragraph>
                Save your app configuration and export as JSON for further development.
              </Paragraph>
            </div>
          </Col>
        </Row>
      </div>

      {/* Call to Action */}
      <div style={{ 
        textAlign: 'center', 
        marginTop: '60px', 
        padding: '40px',
        background: 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)',
        borderRadius: '12px'
      }}>
        <Title level={2}>Ready to Build Your App?</Title>
        <Paragraph style={{ fontSize: '18px', marginBottom: '30px' }}>
          Start with our MVP version and create your first app in minutes.
        </Paragraph>
        <Link to="/mvp">
          <Button 
            type="primary" 
            size="large" 
            icon={<RocketOutlined />}
            style={{ 
              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
              border: 'none',
              height: '50px',
              padding: '0 40px',
              fontSize: '16px'
            }}
          >
            Launch MVP Builder
          </Button>
        </Link>
      </div>
    </div>
  );
};

export default HomePageMVP;
