/**
 * Enhanced Service Worker for App Builder 201
 *
 * This service worker provides offline support, caching, and push notifications
 * for the App Builder application.
 */

// Development mode detection
const isDevelopment = self.location.hostname === 'localhost' ||
  self.location.hostname === '127.0.0.1' ||
  self.location.port === '3000';

// Automatic cache versioning
const CACHE_VERSION = isDevelopment ? 'dev-' + Date.now() : '1.0.0';
const CACHE_NAME = `app-builder-cache-v${CACHE_VERSION}`;
const OFFLINE_URL = '/offline.html';

// Cache configuration
const CACHE_CONFIG = {
  maxAge: isDevelopment ? 1000 * 60 * 5 : 1000 * 60 * 60 * 24, // 5 min dev, 24 hours prod
  maxEntries: 100
};

// Assets to cache - updated to match actual build output
const ASSETS_TO_CACHE = [
  '/',
  '/index.html',
  '/offline.html',
  '/static/css/main.css',
  '/static/js/main.bundle.js',
  '/static/js/bundle.js',
  '/static/js/shared.bundle.js',
  '/static/js/runtime-shared.bundle.js',
  '/manifest.json',
  '/favicon.ico',
  '/logo.svg',
  '/api-offline.json'
];

// Network connectivity helper
function isOnline() {
  return navigator.onLine;
}

// Enhanced fetch with retry logic for network issues
async function fetchWithRetry(request, retries = 2) {
  for (let i = 0; i <= retries; i++) {
    try {
      const response = await fetch(request);
      return response;
    } catch (error) {
      // Check if it's a network connectivity issue
      if (error.name === 'TypeError' && error.message.includes('Failed to fetch')) {
        if (i === retries) {
          throw error; // Last attempt failed
        }
        // Wait before retry (exponential backoff)
        await new Promise(resolve => setTimeout(resolve, Math.pow(2, i) * 1000));
        continue;
      }
      throw error; // Non-network error, don't retry
    }
  }
}

// Install event - cache assets
self.addEventListener('install', (event) => {
  console.log('[Service Worker] Install');

  event.waitUntil(
    caches.open(CACHE_NAME)
      .then((cache) => {
        console.log('[Service Worker] Caching assets');
        // Cache assets individually to handle failures gracefully
        return Promise.allSettled(
          ASSETS_TO_CACHE.map(url =>
            cache.add(url).catch(err => {
              if (isDevelopment) {
                console.warn(`[Service Worker] Failed to cache ${url}:`, err.message);
              }
              return null; // Continue with other assets
            })
          )
        );
      })
      .then(() => {
        console.log('[Service Worker] Installed');
        return self.skipWaiting();
      })
      .catch((error) => {
        console.error('[Service Worker] Install error:', error);
        // Don't fail the installation completely
      })
  );
});

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
  console.log('[Service Worker] Activate');

  event.waitUntil(
    caches.keys()
      .then((cacheNames) => {
        return Promise.all(
          cacheNames.map((cacheName) => {
            if (cacheName !== CACHE_NAME) {
              console.log('[Service Worker] Deleting old cache:', cacheName);
              return caches.delete(cacheName);
            }
          })
        );
      })
      .then(() => {
        console.log('[Service Worker] Activated');
        return self.clients.claim();
      })
      .catch((error) => {
        console.error('[Service Worker] Activate error:', error);
      })
  );
});

// Fetch event - serve from cache or network
self.addEventListener('fetch', (event) => {
  // Skip cross-origin requests
  if (!event.request.url.startsWith(self.location.origin)) {
    return;
  }

  // Skip non-GET requests
  if (event.request.method !== 'GET') {
    return;
  }

  // Skip browser-sync requests
  if (event.request.url.includes('browser-sync')) {
    return;
  }

  // Skip WebSocket requests and Webpack HMR/SockJS requests
  if (
    event.request.url.startsWith('ws:') ||
    event.request.url.startsWith('wss:') ||
    event.request.url.includes('/ws/') ||
    event.request.url.includes('/sockjs-node') ||
    event.request.url.match(/hot-update\.(js|json)$/)
  ) {
    return;
  }

  // Skip API requests - let them go through the normal proxy
  if (event.request.url.includes('/api/')) {
    // Don't intercept API requests, let the main thread handle them
    // This allows the webpack dev server proxy to work correctly
    return;
  }

  // Special handler for test endpoint
  if (event.request.url.includes('/service-worker-test-endpoint')) {
    event.respondWith(
      fetch(event.request)
        .then(response => {
          // Clone the response to modify headers
          const newHeaders = new Headers(response.headers);
          newHeaders.set('X-Service-Worker', 'true');
          
          return new Response(response.body, {
            status: response.status,
            statusText: response.statusText,
            headers: newHeaders
          });
        })
        .catch(error => {
          console.error('[Service Worker] Test endpoint fetch failed:', error);
          return new Response('Service Worker Test Error', {
            status: 500,
            headers: { 'X-Service-Worker': 'true' }
          });
        })
    );
    return;
  }

  // Handle navigation requests (SPA routes)
  if (event.request.mode === 'navigate') {
    event.respondWith(
      fetchWithRetry(event.request)
        .catch((err) => {
          // Only log navigation errors in development mode
          if (isDevelopment) {
            console.warn('[Service Worker] Navigation fetch failed (offline?):', event.request.url, err.message);
          }
          return caches.match(OFFLINE_URL);
        })
    );
    return;
  }

  // Handle other requests with stale-while-revalidate strategy
  event.respondWith(
    caches.match(event.request)
      .then((cachedResponse) => {
        if (cachedResponse) {
          // Revalidate the cache in the background (silently handle failures)
          // Only attempt revalidation if we're online
          if (isOnline()) {
            fetchWithRetry(event.request)
              .then((response) => {
                if (response && response.ok) {
                  const responseForCache = response.clone();
                  caches.open(CACHE_NAME).then((cache) => {
                    cache.put(event.request, responseForCache);
                  }).catch(() => {
                    // Silently handle cache storage failures
                  });
                }
              })
              .catch(() => {
                // Silently handle background fetch failures - this is expected when offline
                // Only log in development mode for debugging
                if (isDevelopment) {
                  console.debug('[Service Worker] Background revalidation failed (offline?):', event.request.url);
                }
              });
          }

          return cachedResponse;
        }

        // No cached response, try network with retry logic
        return fetchWithRetry(event.request)
          .then((response) => {
            if (response && response.ok) {
              const responseForCache = response.clone();
              caches.open(CACHE_NAME).then((cache) => {
                cache.put(event.request, responseForCache);
              }).catch(() => {
                // Silently handle cache storage failures
              });
            }

            return response;
          })
          .catch((err) => {
            // Network request failed, try to return a cached fallback
            // Only log errors in development mode or for critical resources
            const isImportantResource = event.request.url.includes('.html') ||
              event.request.url.includes('.js') ||
              event.request.url.includes('.css');

            if (isDevelopment || isImportantResource) {
              console.warn('[Service Worker] Network fetch failed:', event.request.url, err.message);
            }

            // Try to find a suitable fallback
            const acceptHeader = event.request.headers.get('accept') || '';

            if (acceptHeader.includes('text/html')) {
              return caches.match(OFFLINE_URL);
            }

            if (acceptHeader.includes('image/')) {
              return caches.match('/logo.svg');
            }

            // For favicon specifically, try to return a cached version
            if (event.request.url.includes('favicon.ico')) {
              return caches.match('/favicon.ico').then(cachedFavicon => {
                if (cachedFavicon) {
                  return cachedFavicon;
                }
                // Return a simple 1x1 transparent PNG as fallback
                return new Response(
                  new Uint8Array([
                    0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, 0x00, 0x00, 0x00, 0x0D,
                    0x49, 0x48, 0x44, 0x52, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01,
                    0x08, 0x06, 0x00, 0x00, 0x00, 0x1F, 0x15, 0xC4, 0x89, 0x00, 0x00, 0x00,
                    0x0A, 0x49, 0x44, 0x41, 0x54, 0x78, 0x9C, 0x63, 0x00, 0x01, 0x00, 0x00,
                    0x05, 0x00, 0x01, 0x0D, 0x0A, 0x2D, 0xB4, 0x00, 0x00, 0x00, 0x00, 0x49,
                    0x45, 0x4E, 0x44, 0xAE, 0x42, 0x60, 0x82
                  ]),
                  {
                    status: 200,
                    statusText: 'OK',
                    headers: new Headers({
                      'Content-Type': 'image/png',
                      'Cache-Control': 'public, max-age=86400'
                    })
                  }
                );
              });
            }

            // For other resources, return a minimal response
            return new Response('Resource temporarily unavailable', {
              status: 503,
              statusText: 'Service Unavailable',
              headers: new Headers({
                'Content-Type': 'text/plain',
                'Cache-Control': 'no-cache'
              })
            });
          });
      })
  );
});

// Push event - handle push notifications
self.addEventListener('push', (event) => {
  console.log('[Service Worker] Push received');

  const data = event.data.json();

  const options = {
    body: data.body,
    icon: '/logo.svg',
    badge: '/logo.svg',
    data: {
      url: data.url
    }
  };

  event.waitUntil(
    self.registration.showNotification(data.title, options)
  );
});

// Notification click event - handle notification clicks
self.addEventListener('notificationclick', (event) => {
  console.log('[Service Worker] Notification click');

  event.notification.close();

  event.waitUntil(
    clients.openWindow(event.notification.data.url || '/')
  );
});

// Message event - handle messages from the client
self.addEventListener('message', (event) => {
  console.log('[Service Worker] Message received:', event.data);

  if (event.data.action === 'skipWaiting') {
    self.skipWaiting();
  }
});

