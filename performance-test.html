<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Performance Test Dashboard</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            margin: 20px;
            background: #f0f2f5;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .metric {
            display: inline-block;
            width: 250px;
            margin: 10px;
            padding: 15px;
            border: 1px solid #eee;
            border-radius: 4px;
        }

        .metric h3 {
            margin: 0 0 10px 0;
            color: #666;
        }

        .metric .value {
            font-size: 24px;
            font-weight: bold;
        }

        .good {
            color: #52c41a;
        }

        .warning {
            color: #faad14;
        }

        .error {
            color: #f5222d;
        }

        .controls {
            margin-bottom: 20px;
        }

        button {
            padding: 10px 20px;
            margin-right: 10px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }

        .primary {
            background: #1890ff;
            color: white;
        }

        .danger {
            background: #ff4d4f;
            color: white;
        }

        #chart {
            height: 300px;
            margin: 20px 0;
        }
    </style>
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <script src="browser-performance-test.js"></script>
</head>

<body>
    <div class="container">
        <div class="card">
            <h1>Performance Test Dashboard</h1>
            <div class="controls">
                <button id="startTest" class="primary">Start Test</button>
                <button id="stopTest" class="danger">Stop Test</button>
                <button id="downloadResults">Download Results</button>
            </div>
        </div>

        <div class="card">
            <h2>Real-time Metrics</h2>
            <div id="metrics">
                <div class="metric">
                    <h3>FPS</h3>
                    <div id="fps" class="value">-</div>
                </div>
                <div class="metric">
                    <h3>Memory Usage</h3>
                    <div id="memory" class="value">-</div>
                </div>
                <div class="metric">
                    <h3>DOM Nodes</h3>
                    <div id="domNodes" class="value">-</div>
                </div>
                <div class="metric">
                    <h3>Event Listeners</h3>
                    <div id="eventListeners" class="value">-</div>
                </div>
            </div>
        </div>

        <div class="card">
            <h2>Performance Charts</h2>
            <div id="chart"></div>
        </div>

        <div class="card">
            <h2>Network Requests</h2>
            <div id="networkTable"></div>
        </div>

        <div class="card">
            <h2>Test Results</h2>
            <pre id="results"></pre>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            let browserPerformanceTest;
            let updateInterval;
            let chartData = {
                fps: { x: [], y: [], name: 'FPS' },
                memory: { x: [], y: [], name: 'Memory (MB)' }
            };

            // Initialize browser performance test
            function initializeTest() {
                browserPerformanceTest = new BrowserPerformanceTest({
                    sampleInterval: 1000,
                    testDuration: 30000,
                    saveResults: true,
                    compareWithPrevious: true
                });
            }

            // Update metrics display
            function updateMetrics() {
                if (!browserPerformanceTest || !browserPerformanceTest.performanceTester) return;

                const tester = browserPerformanceTest.performanceTester;
                const metrics = tester.metrics;

                // Update FPS
                const fps = document.getElementById('fps');
                const latestFPS = metrics.frameRate[metrics.frameRate.length - 1]?.fps || 0;
                fps.textContent = latestFPS.toFixed(1);
                fps.className = 'value ' + (latestFPS < 30 ? 'error' : latestFPS < 50 ? 'warning' : 'good');

                // Update Memory
                const memory = document.getElementById('memory');
                const latestMemory = metrics.memory[metrics.memory.length - 1];
                if (latestMemory) {
                    const memoryMB = (latestMemory.usedJSHeapSize / (1024 * 1024)).toFixed(1);
                    memory.textContent = `${memoryMB} MB`;
                }

                // Update DOM Nodes
                const domNodes = document.getElementById('domNodes');
                const latestDOM = metrics.domMetrics[metrics.domMetrics.length - 1];
                if (latestDOM) {
                    domNodes.textContent = latestDOM.nodes;
                }

                // Update Event Listeners (placeholder)
                document.getElementById('eventListeners').textContent = '-';

                // Update charts
                updateCharts();
            }

            // Update charts
            function updateCharts() {
                if (!browserPerformanceTest || !browserPerformanceTest.performanceTester) return;

                const metrics = browserPerformanceTest.performanceTester.metrics;
                const time = performance.now();

                // Add FPS data
                const latestFPS = metrics.frameRate[metrics.frameRate.length - 1];
                if (latestFPS) {
                    chartData.fps.x.push(time);
                    chartData.fps.y.push(latestFPS.fps);
                }

                // Add memory data
                const latestMemory = metrics.memory[metrics.memory.length - 1];
                if (latestMemory) {
                    chartData.memory.x.push(time);
                    chartData.memory.y.push(latestMemory.usedJSHeapSize / (1024 * 1024));
                }

                // Update chart
                if (chartData.fps.x.length > 0) {
                    Plotly.newPlot('chart', [chartData.fps, chartData.memory], {
                        title: 'Performance Metrics Over Time',
                        xaxis: { title: 'Time (ms)' },
                        yaxis: { title: 'Value' }
                    });
                }
            }

            // Start test button
            document.getElementById('startTest').addEventListener('click', () => {
                // Reset chart data
                chartData = {
                    fps: { x: [], y: [], name: 'FPS' },
                    memory: { x: [], y: [], name: 'Memory (MB)' }
                };

                // Initialize and start test
                initializeTest();
                browserPerformanceTest.start();

                // Update metrics every second
                updateInterval = setInterval(() => {
                    updateMetrics();
                }, 1000);

                // Auto-stop after test duration
                setTimeout(() => {
                    if (updateInterval) {
                        clearInterval(updateInterval);
                        updateInterval = null;
                    }
                    if (browserPerformanceTest) {
                        const results = browserPerformanceTest.stop();
                        document.getElementById('results').textContent = JSON.stringify(results, null, 2);
                    }
                }, 30000);
            });

            // Stop test button
            document.getElementById('stopTest').addEventListener('click', () => {
                if (updateInterval) {
                    clearInterval(updateInterval);
                    updateInterval = null;
                }
                if (browserPerformanceTest) {
                    const results = browserPerformanceTest.stop();
                    document.getElementById('results').textContent = JSON.stringify(results, null, 2);
                }
            });

            // Download results button
            document.getElementById('downloadResults').addEventListener('click', () => {
                if (browserPerformanceTest && browserPerformanceTest.performanceTester) {
                    const results = browserPerformanceTest.performanceTester.results;
                    if (results) {
                        const dataStr = JSON.stringify(results, null, 2);
                        const dataBlob = new Blob([dataStr], { type: 'application/json' });
                        const url = URL.createObjectURL(dataBlob);
                        const link = document.createElement('a');
                        link.href = url;
                        link.download = `performance-test-${results.testName || 'results'}.json`;
                        link.click();
                        URL.revokeObjectURL(url);
                    }
                }
            });
        });
    </script>
</body>

</html>