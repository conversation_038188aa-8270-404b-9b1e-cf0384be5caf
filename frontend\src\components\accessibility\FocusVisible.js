import { createGlobalStyle } from 'styled-components';

/**
 * FocusVisible component
 * Adds focus styles to elements that are focused via keyboard navigation
 * but not via mouse clicks
 */
const FocusVisible = createGlobalStyle`
  /* Base focus styles */
  :focus {
    outline: none;
  }

  /* Focus visible styles for keyboard navigation */
  :focus-visible {
    outline: 2px solid ${props => {
    // Handle different theme structures
    if (props.theme?.colorPalette?.primary) {
      return props.theme.colorPalette.primary;
    }
    if (props.theme?.colors?.primary?.main) {
      return props.theme.colors.primary.main;
    }
    if (props.theme?.primaryColor) {
      return props.theme.primaryColor;
    }
    // Fallback color
    return '#2563EB';
  }};
    outline-offset: 2px;
    box-shadow: 0 0 0 4px ${props => {
    // Handle different theme structures for light color
    if (props.theme?.colorPalette?.primaryLight) {
      return props.theme.colorPalette.primaryLight;
    }
    if (props.theme?.colors?.primary?.light) {
      return props.theme.colors.primary.light;
    }
    // Fallback color
    return 'rgba(37, 99, 235, 0.2)';
  }};
  }

  /* Specific focus styles for different elements */
  a:focus-visible,
  button:focus-visible,
  input:focus-visible,
  textarea:focus-visible,
  select:focus-visible,
  [tabindex]:focus-visible {
    outline: 2px solid ${props => {
    if (props.theme?.colorPalette?.primary) return props.theme.colorPalette.primary;
    if (props.theme?.colors?.primary?.main) return props.theme.colors.primary.main;
    if (props.theme?.primaryColor) return props.theme.primaryColor;
    return '#2563EB';
  }};
    outline-offset: 2px;
    box-shadow: 0 0 0 4px ${props => {
    if (props.theme?.colorPalette?.primaryLight) return props.theme.colorPalette.primaryLight;
    if (props.theme?.colors?.primary?.light) return props.theme.colors.primary.light;
    return 'rgba(37, 99, 235, 0.2)';
  }};
  }

  /* High contrast mode focus styles */
  @media (forced-colors: active) {
    :focus-visible {
      outline: 3px solid CanvasText;
      outline-offset: 3px;
    }
  }
`;

export default FocusVisible;
