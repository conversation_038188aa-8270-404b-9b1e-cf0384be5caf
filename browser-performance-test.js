/**
 * Comprehensive Browser Performance Test
 * 
 * This script provides detailed performance analysis across different browsers
 * and identifies potential bottlenecks with specific recommendations.
 */

class BrowserPerformanceTest {
  constructor(options = {}) {
    this.options = {
      testDuration: options.testDuration || 30000, // 30 seconds
      sampleInterval: options.sampleInterval || 1000, // 1 second
      testName: options.testName || `browser-test-${new Date().toISOString().replace(/[:.]/g, '-')}`,
      saveResults: options.saveResults !== undefined ? options.saveResults : true,
      compareWithPrevious: options.compareWithPrevious !== undefined ? options.compareWithPrevious : true,
      ...options
    };
    
    this.performanceTester = null;
    this.browserInfo = this.detectBrowser();
    this.previousResults = this.loadPreviousResults();
    this.recommendations = [];
  }
  
  /**
   * Detect browser type and version
   */
  detectBrowser() {
    const userAgent = navigator.userAgent;
    let browser = {
      name: 'unknown',
      version: 'unknown',
      engine: 'unknown',
      os: 'unknown',
      mobile: false
    };
    
    // Detect browser name and version
    if (userAgent.indexOf('Edge') > -1 || userAgent.indexOf('Edg/') > -1) {
      browser.name = 'Edge';
      browser.engine = 'Blink';
      const edgeMatch = userAgent.match(/(Edge|Edg)\/(\d+(\.\d+)?)/);
      if (edgeMatch) browser.version = edgeMatch[2];
    } else if (userAgent.indexOf('Firefox') > -1) {
      browser.name = 'Firefox';
      browser.engine = 'Gecko';
      const firefoxMatch = userAgent.match(/Firefox\/(\d+(\.\d+)?)/);
      if (firefoxMatch) browser.version = firefoxMatch[1];
    } else if (userAgent.indexOf('Safari') > -1 && userAgent.indexOf('Chrome') === -1) {
      browser.name = 'Safari';
      browser.engine = 'WebKit';
      const safariMatch = userAgent.match(/Version\/(\d+(\.\d+)?)/);
      if (safariMatch) browser.version = safariMatch[1];
    } else if (userAgent.indexOf('Chrome') > -1) {
      browser.name = 'Chrome';
      browser.engine = 'Blink';
      const chromeMatch = userAgent.match(/Chrome\/(\d+(\.\d+)?)/);
      if (chromeMatch) browser.version = chromeMatch[1];
    }
    
    // Detect OS
    if (userAgent.indexOf('Windows') > -1) {
      browser.os = 'Windows';
    } else if (userAgent.indexOf('Mac') > -1) {
      browser.os = 'macOS';
    } else if (userAgent.indexOf('Linux') > -1) {
      browser.os = 'Linux';
    } else if (userAgent.indexOf('Android') > -1) {
      browser.os = 'Android';
      browser.mobile = true;
    } else if (userAgent.indexOf('iOS') > -1 || userAgent.indexOf('iPhone') > -1 || userAgent.indexOf('iPad') > -1) {
      browser.os = 'iOS';
      browser.mobile = true;
    }
    
    // Add feature detection
    browser.features = {
      webGL: this.detectWebGL(),
      webGL2: this.detectWebGL2(),
      webWorkers: typeof Worker !== 'undefined',
      serviceWorkers: 'serviceWorker' in navigator,
      webAssembly: typeof WebAssembly !== 'undefined',
      sharedArrayBuffer: typeof SharedArrayBuffer !== 'undefined',
      indexedDB: 'indexedDB' in window,
      webRTC: 'RTCPeerConnection' in window,
      webAudio: 'AudioContext' in window || 'webkitAudioContext' in window,
      webSpeech: 'SpeechRecognition' in window || 'webkitSpeechRecognition' in window,
      webVR: 'getVRDisplays' in navigator,
      webXR: 'xr' in navigator,
      touchEvents: 'ontouchstart' in window,
      performanceAPI: 'performance' in window && 'measure' in performance,
      memoryInfo: !!(performance && performance.memory)
    };
    
    return browser;
  }
  
  /**
   * Detect WebGL support
   */
  detectWebGL() {
    try {
      const canvas = document.createElement('canvas');
      return !!(window.WebGLRenderingContext && 
        (canvas.getContext('webgl') || canvas.getContext('experimental-webgl')));
    } catch (e) {
      return false;
    }
  }
  
  /**
   * Detect WebGL2 support
   */
  detectWebGL2() {
    try {
      const canvas = document.createElement('canvas');
      return !!(window.WebGL2RenderingContext && canvas.getContext('webgl2'));
    } catch (e) {
      return false;
    }
  }
  
  /**
   * Load previous test results for comparison
   */
  loadPreviousResults() {
    if (!this.options.compareWithPrevious) return null;
    
    try {
      const previousResults = JSON.parse(localStorage.getItem('performanceTestResults') || '[]');
      if (previousResults.length === 0) return null;
      
      // Get the most recent result for the same browser
      const sameEnvResults = previousResults.filter(result => 
        result.browserInfo && result.browserInfo.name === this.browserInfo.name);
      
      if (sameEnvResults.length === 0) return null;
      
      // Sort by timestamp (newest first)
      sameEnvResults.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
      
      // Get full details of the most recent test
      const mostRecentTest = sameEnvResults[0];
      return JSON.parse(localStorage.getItem(`performanceTest_${mostRecentTest.testName}`)) || null;
    } catch (error) {
      console.error('Failed to load previous results:', error);
      return null;
    }
  }
  
  /**
   * Start the performance test
   */
  start() {
    console.log(`Starting browser performance test: ${this.options.testName}`);
    console.log(`Browser detected: ${this.browserInfo.name} ${this.browserInfo.version} (${this.browserInfo.engine}) on ${this.browserInfo.os}`);
    
    // Initialize the performance tester
    this.performanceTester = new PerformanceTester({
      testDuration: this.options.testDuration,
      sampleInterval: this.options.sampleInterval,
      testName: this.options.testName,
      saveResults: this.options.saveResults
    });
    
    // Start collecting metrics
    this.performanceTester.start();
    
    // Set timeout to stop the test after the specified duration
    setTimeout(() => {
      this.stop();
    }, this.options.testDuration);
    
    return this;
  }
  
  /**
   * Stop the test and generate a report
   */
  stop() {
    if (!this.performanceTester || !this.performanceTester.isRunning) {
      console.warn('No performance test is currently running');
      return;
    }
    
    // Stop the performance tester
    const results = this.performanceTester.stop();
    
    // Add browser information to results
    results.browserInfo = this.browserInfo;
    
    // Analyze results and generate recommendations
    this.analyzeResults(results);
    
    // Save enhanced results
    if (this.options.saveResults) {
      localStorage.setItem(`performanceTest_${this.options.testName}`, JSON.stringify(results));
      
      // Update the summary list
      const summaryList = JSON.parse(localStorage.getItem('performanceTestResults') || '[]');
      summaryList.push({
        testName: results.testName,
        timestamp: results.timestamp,
        browserInfo: results.browserInfo,
        summary: results.summary,
        recommendations: this.recommendations
      });
      localStorage.setItem('performanceTestResults', JSON.stringify(summaryList));
    }
    
    // Return the enhanced results
    return {
      ...results,
      recommendations: this.recommendations,
      comparison: this.compareWithPrevious(results)
    };
  }
  
  /**
   * Analyze results and generate recommendations
   */
  analyzeResults(results) {
    this.recommendations = [];
    
    // Frame rate analysis
    if (results.summary.avgFrameRate < 30) {
      this.recommendations.push({
        category: 'Performance',
        severity: 'high',
        issue: 'Low frame rate detected',
        description: `Average frame rate is ${results.summary.avgFrameRate.toFixed(1)} FPS, which is below the recommended 60 FPS.`,
        solution: this.getFrameRateRecommendations(results)
      });
    }
    
    // Memory usage analysis
    if (results.browserInfo.features.memoryInfo) {
      const memoryUsage = results.summary.totalJSHeapSize / (1024 * 1024);
      if (memoryUsage > 100) {
        this.recommendations.push({
          category: 'Memory',
          severity: 'medium',
          issue: 'High memory usage',
          description: `Memory usage peaked at ${memoryUsage.toFixed(1)} MB.`,
          solution: this.getMemoryRecommendations(results)
        });
      }
    }
    
    // Long tasks analysis
    if (results.details.longTasks.count > 0) {
      const avgTaskDuration = results.details.longTasks.avgDuration;
      if (avgTaskDuration > 100) {
        this.recommendations.push({
          category: 'Responsiveness',
          severity: 'high',
          issue: 'Long-running JavaScript tasks detected',
          description: `${results.details.longTasks.count} long tasks with average duration of ${avgTaskDuration.toFixed(1)}ms.`,
          solution: this.getLongTaskRecommendations(results)
        });
      }
    }
    
    // Network requests analysis
    if (results.details.network.failedRequests > 0) {
      this.recommendations.push({
        category: 'Network',
        severity: 'high',
        issue: 'Failed network requests',
        description: `${results.details.network.failedRequests} network requests failed.`,
        solution: 'Check the network tab for failed requests and ensure all resources are available.'
      });
    }
    
    if (results.details.network.avgDuration > 500) {
      this.recommendations.push({
        category: 'Network',
        severity: 'medium',
        issue: 'Slow network requests',
        description: `Average network request duration is ${results.details.network.avgDuration.toFixed(1)}ms.`,
        solution: 'Consider implementing caching, compression, or a CDN for frequently accessed resources.'
      });
    }
    
    // DOM size analysis
    if (results.details.domMetrics.nodes.max > 1000) {
      this.recommendations.push({
        category: 'DOM',
        severity: 'medium',
        issue: 'Large DOM size',
        description: `Maximum DOM node count is ${results.details.domMetrics.nodes.max}.`,
        solution: 'Consider virtualizing large lists, removing unnecessary DOM elements, or implementing pagination.'
      });
    }
    
    // Browser-specific recommendations
    this.addBrowserSpecificRecommendations(results);
  }
  
  /**
   * Get frame rate recommendations based on results
   */
  getFrameRateRecommendations(results) {
    const recommendations = [
      'Reduce DOM complexity and minimize style recalculations',
      'Use requestAnimationFrame for animations instead of setTimeout/setInterval',
      'Implement CSS transitions/animations instead of JavaScript animations where possible',
      'Consider using Web Workers for CPU-intensive tasks'
    ];
    
    // Browser-specific recommendations
    if (this.browserInfo.name === 'Chrome' || this.browserInfo.name === 'Edge') {
      recommendations.push('Enable hardware acceleration in browser settings');
      recommendations.push('Disable unnecessary Chrome extensions that might impact performance');
    } else if (this.browserInfo.name === 'Firefox') {
      recommendations.push('Set layers.acceleration.force-enabled to true in about:config');
    } else if (this.browserInfo.name === 'Safari') {
      recommendations.push('Check for Safari extensions that might be impacting performance');
    }
    
    return recommendations;
  }
  
  /**
   * Get memory recommendations based on results
   */
  getMemoryRecommendations(results) {
    return [
      'Check for memory leaks by using the Memory tab in DevTools',
      'Implement proper cleanup of event listeners and DOM elements',
      'Consider implementing object pooling for frequently created/destroyed objects',
      'Use smaller asset sizes and optimize images',
      'Implement lazy loading for resources not immediately needed'
    ];
  }
  
  /**
   * Get long task recommendations based on results
   */
  getLongTaskRecommendations(results) {
    return [
      'Break up long-running JavaScript tasks into smaller chunks',
      'Use requestIdleCallback for non-critical work',
      'Move CPU-intensive operations to Web Workers',
      'Implement code splitting to reduce initial JavaScript parsing time',
      'Optimize event handlers, especially for scroll, resize, and input events'
    ];
  }
  
  /**
   * Add browser-specific recommendations
   */
  addBrowserSpecificRecommendations(results) {
    // Chrome-specific recommendations
    if (this.browserInfo.name === 'Chrome') {
      if (!results.browserInfo.features.memoryInfo) {
        this.recommendations.push({
          category: 'Browser',
          severity: 'low',
          issue: 'Limited performance API access',
          description: 'Chrome is not providing memory usage information.',
          solution: 'Run Chrome with --enable-precise-memory-info flag for better diagnostics.'
        });
      }
    }
    
    // Firefox-specific recommendations
    else if (this.browserInfo.name === 'Firefox') {
      if (results.details.longTasks.count === 0 && results.options.testDuration > 5000) {
        this.recommendations.push({
          category: 'Browser',
          severity: 'low',
          issue: 'Long Tasks API not fully supported',
          description: 'Firefox may not report long tasks correctly.',
          solution: 'Use Firefox Developer Edition or Nightly for better performance monitoring support.'
        });
      }
    }
    
    // Safari-specific recommendations
    else if (this.browserInfo.name === 'Safari') {
      this.recommendations.push({
        category: 'Browser',
        severity: 'low',
        issue: 'Limited Performance API support',
        description: 'Safari has limited support for the Performance API.',
        solution: 'Consider using Chrome or Firefox for more detailed performance diagnostics.'
      });
      
      if (results.summary.avgFrameRate < 30) {
        this.recommendations.push({
          category: 'Browser',
          severity: 'medium',
          issue: 'Safari rendering performance',
          description: 'Safari may have different rendering behavior than other browsers.',
          solution: 'Test with multiple browsers and optimize specifically for Safari if it\'s a target browser.'
        });
      }
    }
  }
  
  /**
   * Compare current results with previous results
   */
  compareWithPrevious(currentResults) {
    if (!this.previousResults) return null;
    
    const comparison = {
      testName: this.previousResults.testName,
      timestamp: this.previousResults.timestamp,
      changes: {}
    };
    
    // Compare key metrics
    comparison.changes.frameRate = {
      previous: this.previousResults.summary.avgFrameRate,
      current: currentResults.summary.avgFrameRate,
      change: ((currentResults.summary.avgFrameRate - this.previousResults.summary.avgFrameRate) / 
               this.previousResults.summary.avgFrameRate * 100).toFixed(1) + '%',
      improved: currentResults.summary.avgFrameRate > this.previousResults.summary.avgFrameRate
    };
    
    if (currentResults.summary.totalJSHeapSize && this.previousResults.summary.totalJSHeapSize) {
      comparison.changes.memory = {
        previous: this.previousResults.summary.totalJSHeapSize / (1024 * 1024),
        current: currentResults.summary.totalJSHeapSize / (1024 * 1024),
        change: ((currentResults.summary.totalJSHeapSize - this.previousResults.summary.totalJSHeapSize) / 
                 this.previousResults.summary.totalJSHeapSize * 100).toFixed(1) + '%',
        improved: currentResults.summary.totalJSHeapSize <= this.previousResults.summary.totalJSHeapSize
      };
    }
    
    comparison.changes.longTasks = {
      previous: this.previousResults.details.longTasks.count,
      current: currentResults.details.longTasks.count,
      change: ((currentResults.details.longTasks.count - this.previousResults.details.longTasks.count) / 
               (this.previousResults.details.longTasks.count || 1) * 100).toFixed(1) + '%',
      improved: currentResults.details.longTasks.count <= this.previousResults.details.longTasks.count
    };
    
    comparison.changes.domNodes = {
      previous: this.previousResults.details.domMetrics.nodes.max,
      current: currentResults.details.domMetrics.nodes.max,
      change: ((currentResults.details.domMetrics.nodes.max - this.previousResults.details.domMetrics.nodes.max) / 
               this.previousResults.details.domMetrics.nodes.max * 100).toFixed(1) + '%',
      improved: currentResults.details.domMetrics.nodes.max <= this.previousResults.details.domMetrics.nodes.max
    };
    
    return comparison;
  }
  
  /**
   * Generate an HTML report
   */
  generateHTMLReport(results) {
    if (!results) {
      console.warn('No results available to generate report');
      return '';
    }
    
    const comparison = results.comparison;
    
    return `
      <div class="performance-report">
        <h2>Browser Performance Report</h2>
        <div class="report-meta">
          <p><strong>Test:</strong> ${results.testName}</p>
          <p><strong>Date:</strong> ${new Date(results.timestamp).toLocaleString()}</p>
          <p><strong>Duration:</strong> ${(results.testDuration / 1000).toFixed(1)}s</p>
          <p><strong>Browser:</strong> ${results.browserInfo.name} ${results.browserInfo.version} on ${results.browserInfo.os}</p>
        </div>
        
        <h3>Summary</h3>
        <div class="metrics-summary">
          <div class="metric ${results.summary.avgFrameRate < 30 ? 'warning' : 'good'}">
            <h4>Frame Rate</h4>
            <div class="value">${results.summary.avgFrameRate.toFixed(1)} FPS</div>
            ${comparison ? `
              <div class="comparison ${comparison.changes.frameRate.improved ? 'improved' : 'degraded'}">
                ${comparison.changes.frameRate.change} from previous
              </div>
            ` : ''}
          </div>
          
          <div class="metric ${results.summary.totalJSHeapSize / (1024 * 1024) > 100 ? 'warning' : 'good'}">
            <h4>Memory Usage</h4>
            <div class="value">${(results.summary.totalJSHeapSize / (1024 * 1024)).toFixed(1)} MB</div>
            ${comparison && comparison.changes.memory ? `
              <div class="comparison ${comparison.changes.memory.improved ? 'improved' : 'degraded'}">
                ${comparison.changes.memory.change} from previous
              </div>
            ` : ''}