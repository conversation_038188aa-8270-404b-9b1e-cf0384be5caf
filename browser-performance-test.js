/**
 * Comprehensive Browser Performance Test
 * 
 * This script provides detailed performance analysis across different browsers
 * and identifies potential bottlenecks with specific recommendations.
 */

class BrowserPerformanceTest {
  constructor(options = {}) {
    this.options = {
      testDuration: options.testDuration || 30000, // 30 seconds
      sampleInterval: options.sampleInterval || 1000, // 1 second
      testName: options.testName || `browser-test-${new Date().toISOString().replace(/[:.]/g, '-')}`,
      saveResults: options.saveResults !== undefined ? options.saveResults : true,
      compareWithPrevious: options.compareWithPrevious !== undefined ? options.compareWithPrevious : true,
      ...options
    };
    
    this.performanceTester = null;
    this.browserInfo = this.detectBrowser();
    this.previousResults = this.loadPreviousResults();
    this.recommendations = [];
  }
  
  /**
   * Detect browser type and version
   */
  detectBrowser() {
    const userAgent = navigator.userAgent;
    let browser = {
      name: 'unknown',
      version: 'unknown',
      engine: 'unknown',
      os: 'unknown',
      mobile: false
    };
    
    // Detect browser name and version
    if (userAgent.indexOf('Edge') > -1 || userAgent.indexOf('Edg/') > -1) {
      browser.name = 'Edge';
      browser.engine = 'Blink';
      const edgeMatch = userAgent.match(/(Edge|Edg)\/(\d+(\.\d+)?)/);
      if (edgeMatch) browser.version = edgeMatch[2];
    } else if (userAgent.indexOf('Firefox') > -1) {
      browser.name = 'Firefox';
      browser.engine = 'Gecko';
      const firefoxMatch = userAgent.match(/Firefox\/(\d+(\.\d+)?)/);
      if (firefoxMatch) browser.version = firefoxMatch[1];
    } else if (userAgent.indexOf('Safari') > -1 && userAgent.indexOf('Chrome') === -1) {
      browser.name = 'Safari';
      browser.engine = 'WebKit';
      const safariMatch = userAgent.match(/Version\/(\d+(\.\d+)?)/);
      if (safariMatch) browser.version = safariMatch[1];
    } else if (userAgent.indexOf('Chrome') > -1) {
      browser.name = 'Chrome';
      browser.engine = 'Blink';
      const chromeMatch = userAgent.match(/Chrome\/(\d+(\.\d+)?)/);
      if (chromeMatch) browser.version = chromeMatch[1];
    }
    
    // Detect OS
    if (userAgent.indexOf('Windows') > -1) {
      browser.os = 'Windows';
    } else if (userAgent.indexOf('Mac') > -1) {
      browser.os = 'macOS';
    } else if (userAgent.indexOf('Linux') > -1) {
      browser.os = 'Linux';
    } else if (userAgent.indexOf('Android') > -1) {
      browser.os = 'Android';
      browser.mobile = true;
    } else if (userAgent.indexOf('iOS') > -1 || userAgent.indexOf('iPhone') > -1 || userAgent.indexOf('iPad') > -1) {
      browser.os = 'iOS';
      browser.mobile = true;
    }
    
    // Add feature detection
    browser.features = {
      webGL: this.detectWebGL(),
      webGL2: this.detectWebGL2(),
      webWorkers: typeof Worker !== 'undefined',
      serviceWorkers: 'serviceWorker' in navigator,
      webAssembly: typeof WebAssembly !== 'undefined',
      sharedArrayBuffer: typeof SharedArrayBuffer !== 'undefined',
      indexedDB: 'indexedDB' in window,
      webRTC: 'RTCPeerConnection' in window,
      webAudio: 'AudioContext' in window || 'webkitAudioContext' in window,
      webSpeech: 'SpeechRecognition' in window || 'webkitSpeechRecognition' in window,
      webVR: 'getVRDisplays' in navigator,
      webXR: 'xr' in navigator,
      touchEvents: 'ontouchstart' in window,
      performanceAPI: 'performance' in window && 'measure' in performance,
      memoryInfo: !!(performance && performance.memory)
    };
    
    return browser;
  }
  
  /**
   * Detect WebGL support
   */
  detectWebGL() {
    try {
      const canvas = document.createElement('canvas');
      return !!(window.WebGLRenderingContext && 
        (canvas.getContext('webgl') || canvas.getContext('experimental-webgl')));
    } catch (e) {
      return false;
    }
  }
  
  /**
   * Detect WebGL2 support
   */
  detectWebGL2() {
    try {
      const canvas = document.createElement('canvas');
      return !!(window.WebGL2RenderingContext && canvas.getContext('webgl2'));
    } catch (e) {
      return false;
    }
  }
  
  /**
   * Load previous test results for comparison
   */
  loadPreviousResults() {
    if (!this.options.compareWithPrevious) return null;
    
    try {
      const previousResults = JSON.parse(localStorage.getItem('performanceTestResults') || '[]');
      if (previousResults.length === 0) return null;
      
      // Get the most recent result for the same browser
      const sameEnvResults = previousResults.filter(result => 
        result.browserInfo && result.browserInfo.name === this.browserInfo.name);
      
      if (sameEnvResults.length === 0) return null;
      
      // Sort by timestamp (newest first)
      sameEnvResults.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
      
      // Get full details of the most recent test
      const mostRecentTest = sameEnvResults[0];
      return JSON.parse(localStorage.getItem(`performanceTest_${mostRecentTest.testName}`)) || null;
    } catch (error) {
      console.error('Failed to load previous results:', error);
      return null;
    }
  }
  
  /**
   * Start the performance test
   */
  start() {
    console.log(`Starting browser performance test: ${this.options.testName}`);
    console.log(`Browser detected: ${this.browserInfo.name} ${this.browserInfo.version} (${this.browserInfo.engine}) on ${this.browserInfo.os}`);
    
    // Initialize the performance tester
    this.performanceTester = new PerformanceTester({
      testDuration: this.options.testDuration,
      sampleInterval: this.options.sampleInterval,
      testName: this.options.testName,
      saveResults: this.options.saveResults
    });
    
    // Start collecting metrics
    this.performanceTester.start();
    
    // Set timeout to stop the test after the specified duration
    setTimeout(() => {
      this.stop();
    }, this.options.testDuration);
    
    return this;
  }
  
  /**
   * Stop the test and generate a report
   */
  stop() {
    if (!this.performanceTester || !this.performanceTester.isRunning) {
      console.warn('No performance test is currently running');
      return;
    }
    
    // Stop the performance tester
    const results = this.performanceTester.stop();
    
    // Add browser information to results
    results.browserInfo = this.browserInfo;
    
    // Analyze results and generate recommendations
    this.analyzeResults(results);
    
    // Save enhanced results
    if (this.options.saveResults) {
      localStorage.setItem(`performanceTest_${this.options.testName}`, JSON.stringify(results));
      
      // Update the summary list
      const summaryList = JSON.parse(localStorage.getItem('performanceTestResults') || '[]');
      summaryList.push({
        testName: results.testName,
        timestamp: results.timestamp,
        browserInfo: results.browserInfo,
        summary: results.summary,
        recommendations: this.recommendations
      });
      localStorage.setItem('performanceTestResults', JSON.stringify(summaryList));
    }
    
    // Return the enhanced results
    return {
      ...results,
      recommendations: this.recommendations,
      comparison: this.compareWithPrevious(results)
    };
  }
  
  /**
   * Analyze results and generate recommendations
   */
  analyzeResults(results) {
    this.recommendations = [];
    
    // Frame rate analysis
    if (results.summary.avgFrameRate < 30) {
      this.recommendations.push({
        category: 'Performance',
        severity: 'high',
        issue: 'Low frame rate detected',
        description: `Average frame rate is ${results.summary.avgFrameRate.toFixed(1)} FPS, which is below the recommended 60 FPS.`,
        solution: this.getFrameRateRecommendations(results)
      });
    }
    
    // Memory usage analysis
    if (results.browserInfo.features.memoryInfo) {
      const memoryUsage = results.summary.totalJSHeapSize / (1024 * 1024);
      if (memoryUsage > 100) {
        this.recommendations.push({
          category: 'Memory',
          severity: 'medium',
          issue: 'High memory usage',
          description: `Memory usage peaked at ${memoryUsage.toFixed(1)} MB.`,
          solution: this.getMemoryRecommendations(results)
        });
      }
    }
    
    // Long tasks analysis
    if (results.details.longTasks.count > 0) {
      const avgTaskDuration = results.details.longTasks.avgDuration;
      if (avgTaskDuration > 100) {
        this.recommendations.push({
          category: 'Responsiveness',
          severity: 'high',
          issue: 'Long-running JavaScript tasks detected',
          description: `${results.details.longTasks.count} long tasks with average duration of ${avgTaskDuration.toFixed(1)}ms.`,
          solution: this.getLongTaskRecommendations(results)
        });
      }
    }
    
    // Network requests analysis
    if (results.details.network.failedRequests > 0) {
      this.recommendations.push({
        category: 'Network',
        severity: 'high',
        issue: 'Failed network requests',
        description: `${results.details.network.failedRequests} network requests failed.`,
        solution: 'Check the network tab for failed requests and ensure all resources are available.'
      });
    }
    
    if (results.details.network.avgDuration > 500) {
      this.recommendations.push({
        category: 'Network',
        severity: 'medium',
        issue: 'Slow network requests',
        description: `Average network request duration is ${results.details.network.avgDuration.toFixed(1)}ms.`,
        solution: 'Consider implementing caching, compression, or a CDN for frequently accessed resources.'
      });
    }
    
    // DOM size analysis
    if (results.details.domMetrics.nodes.max > 1000) {
      this.recommendations.push({
        category: 'DOM',
        severity: 'medium',
        issue: 'Large DOM size',
        description: `Maximum DOM node count is ${results.details.domMetrics.nodes.max}.`,
        solution: 'Consider virtualizing large lists, removing unnecessary DOM elements, or implementing pagination.'
      });
    }
    
    // Browser-specific recommendations
    this.addBrowserSpecificRecommendations(results);
  }
  
  /**
   * Get frame rate recommendations based on results
   */
  getFrameRateRecommendations(results) {
    const recommendations = [
      'Reduce DOM complexity and minimize style recalculations',
      'Use requestAnimationFrame for animations instead of setTimeout/setInterval',
      'Implement CSS transitions/animations instead of JavaScript animations where possible',
      'Consider using Web Workers for CPU-intensive tasks'
    ];
    
    // Browser-specific recommendations
    if (this.browserInfo.name === 'Chrome' || this.browserInfo.name === 'Edge') {
      recommendations.push('Enable hardware acceleration in browser settings');
      recommendations.push('Disable unnecessary Chrome extensions that might impact performance');
    } else if (this.browserInfo.name === 'Firefox') {
      recommendations.push('Set layers.acceleration.force-enabled to true in about:config');
    } else if (this.browserInfo.name === 'Safari') {
      recommendations.push('Check for Safari extensions that might be impacting performance');
    }
    
    return recommendations;
  }
  
  /**
   * Get memory recommendations based on results
   */
  getMemoryRecommendations(results) {
    return [
      'Check for memory leaks by using the Memory tab in DevTools',
      'Implement proper cleanup of event listeners and DOM elements',
      'Consider implementing object pooling for frequently created/destroyed objects',
      'Use smaller asset sizes and optimize images',
      'Implement lazy loading for resources not immediately needed'
    ];
  }
  
  /**
   * Get long task recommendations based on results
   */
  getLongTaskRecommendations(results) {
    return [
      'Break up long-running JavaScript tasks into smaller chunks',
      'Use requestIdleCallback for non-critical work',
      'Move CPU-intensive operations to Web Workers',
      'Implement code splitting to reduce initial JavaScript parsing time',
      'Optimize event handlers, especially for scroll, resize, and input events'
    ];
  }
  
  /**
   * Add browser-specific recommendations
   */
  addBrowserSpecificRecommendations(results) {
    // Chrome-specific recommendations
    if (this.browserInfo.name === 'Chrome') {
      if (!results.browserInfo.features.memoryInfo) {
        this.recommendations.push({
          category: 'Browser',
          severity: 'low',
          issue: 'Limited performance API access',
          description: 'Chrome is not providing memory usage information.',
          solution: 'Run Chrome with --enable-precise-memory-info flag for better diagnostics.'
        });
      }
    }
    
    // Firefox-specific recommendations
    else if (this.browserInfo.name === 'Firefox') {
      if (results.details.longTasks.count === 0 && results.options.testDuration > 5000) {
        this.recommendations.push({
          category: 'Browser',
          severity: 'low',
          issue: 'Long Tasks API not fully supported',
          description: 'Firefox may not report long tasks correctly.',
          solution: 'Use Firefox Developer Edition or Nightly for better performance monitoring support.'
        });
      }
    }
    
    // Safari-specific recommendations
    else if (this.browserInfo.name === 'Safari') {
      this.recommendations.push({
        category: 'Browser',
        severity: 'low',
        issue: 'Limited Performance API support',
        description: 'Safari has limited support for the Performance API.',
        solution: 'Consider using Chrome or Firefox for more detailed performance diagnostics.'
      });
      
      if (results.summary.avgFrameRate < 30) {
        this.recommendations.push({
          category: 'Browser',
          severity: 'medium',
          issue: 'Safari rendering performance',
          description: 'Safari may have different rendering behavior than other browsers.',
          solution: 'Test with multiple browsers and optimize specifically for Safari if it\'s a target browser.'
        });
      }
    }
  }
  
  /**
   * Compare current results with previous results
   */
  compareWithPrevious(currentResults) {
    if (!this.previousResults) return null;
    
    const comparison = {
      testName: this.previousResults.testName,
      timestamp: this.previousResults.timestamp,
      changes: {}
    };
    
    // Compare key metrics
    comparison.changes.frameRate = {
      previous: this.previousResults.summary.avgFrameRate,
      current: currentResults.summary.avgFrameRate,
      change: ((currentResults.summary.avgFrameRate - this.previousResults.summary.avgFrameRate) / 
               this.previousResults.summary.avgFrameRate * 100).toFixed(1) + '%',
      improved: currentResults.summary.avgFrameRate > this.previousResults.summary.avgFrameRate
    };
    
    if (currentResults.summary.totalJSHeapSize && this.previousResults.summary.totalJSHeapSize) {
      comparison.changes.memory = {
        previous: this.previousResults.summary.totalJSHeapSize / (1024 * 1024),
        current: currentResults.summary.totalJSHeapSize / (1024 * 1024),
        change: ((currentResults.summary.totalJSHeapSize - this.previousResults.summary.totalJSHeapSize) / 
                 this.previousResults.summary.totalJSHeapSize * 100).toFixed(1) + '%',
        improved: currentResults.summary.totalJSHeapSize <= this.previousResults.summary.totalJSHeapSize
      };
    }
    
    comparison.changes.longTasks = {
      previous: this.previousResults.details.longTasks.count,
      current: currentResults.details.longTasks.count,
      change: ((currentResults.details.longTasks.count - this.previousResults.details.longTasks.count) / 
               (this.previousResults.details.longTasks.count || 1) * 100).toFixed(1) + '%',
      improved: currentResults.details.longTasks.count <= this.previousResults.details.longTasks.count
    };
    
    comparison.changes.domNodes = {
      previous: this.previousResults.details.domMetrics.nodes.max,
      current: currentResults.details.domMetrics.nodes.max,
      change: ((currentResults.details.domMetrics.nodes.max - this.previousResults.details.domMetrics.nodes.max) / 
               this.previousResults.details.domMetrics.nodes.max * 100).toFixed(1) + '%',
      improved: currentResults.details.domMetrics.nodes.max <= this.previousResults.details.domMetrics.nodes.max
    };
    
    return comparison;
  }
  
  /**
   * Generate an HTML report
   */
  generateHTMLReport(results) {
    if (!results) {
      console.warn('No results available to generate report');
      return '';
    }
    
    const comparison = results.comparison;
    
    return `
      <div class="performance-report">
        <h2>Browser Performance Report</h2>
        <div class="report-meta">
          <p><strong>Test:</strong> ${results.testName}</p>
          <p><strong>Date:</strong> ${new Date(results.timestamp).toLocaleString()}</p>
          <p><strong>Duration:</strong> ${(results.testDuration / 1000).toFixed(1)}s</p>
          <p><strong>Browser:</strong> ${results.browserInfo.name} ${results.browserInfo.version} on ${results.browserInfo.os}</p>
        </div>
        
        <h3>Summary</h3>
        <div class="metrics-summary">
          <div class="metric ${results.summary.avgFrameRate < 30 ? 'warning' : 'good'}">
            <h4>Frame Rate</h4>
            <div class="value">${results.summary.avgFrameRate.toFixed(1)} FPS</div>
            ${comparison ? `
              <div class="comparison ${comparison.changes.frameRate.improved ? 'improved' : 'degraded'}">
                ${comparison.changes.frameRate.change} from previous
              </div>
            ` : ''}
          </div>
          
          <div class="metric ${results.summary.totalJSHeapSize / (1024 * 1024) > 100 ? 'warning' : 'good'}">
            <h4>Memory Usage</h4>
            <div class="value">${(results.summary.totalJSHeapSize / (1024 * 1024)).toFixed(1)} MB</div>
            ${comparison && comparison.changes.memory ? `
              <div class="comparison ${comparison.changes.memory.improved ? 'improved' : 'degraded'}">
                ${comparison.changes.memory.change} from previous
              </div>
            ` : ''}
          </div>
          
          <div class="metric ${results.details.longTasks.count > 5 ? 'warning' : 'good'}">
            <h4>Long Tasks</h4>
            <div class="value">${results.details.longTasks.count}</div>
            ${comparison ? `
              <div class="comparison ${comparison.changes.longTasks.improved ? 'improved' : 'degraded'}">
                ${comparison.changes.longTasks.change} from previous
              </div>
            ` : ''}
          </div>
          
          <div class="metric ${results.details.domMetrics.nodes.max > 1000 ? 'warning' : 'good'}">
            <h4>DOM Size</h4>
            <div class="value">${results.details.domMetrics.nodes.max} nodes</div>
            ${comparison ? `
              <div class="comparison ${comparison.changes.domNodes.improved ? 'improved' : 'degraded'}">
                ${comparison.changes.domNodes.change} from previous
              </div>
            ` : ''}
          </div>
        </div>
        
        <h3>Recommendations</h3>
        <div class="recommendations">
          ${results.recommendations.length > 0 ? results.recommendations.map(rec => `
            <div class="recommendation ${rec.severity}">
              <h4>${rec.category}: ${rec.issue}</h4>
              <p>${rec.description}</p>
              <div class="solution">
                ${Array.isArray(rec.solution) ? 
                  `<ul>${rec.solution.map(sol => `<li>${sol}</li>`).join('')}</ul>` : 
                  `<p>${rec.solution}</p>`
                }
              </div>
            </div>
          `).join('') : '<p>No issues detected. Your application is performing well!</p>'}
        </div>
        
        <h3>Detailed Metrics</h3>
        <div class="detailed-metrics">
          <h4>Frame Rate</h4>
          <p>Min: ${results.details.frameRate.min.toFixed(1)} FPS</p>
          <p>Max: ${results.details.frameRate.max.toFixed(1)} FPS</p>
          <p>Avg: ${results.details.frameRate.avg.toFixed(1)} FPS</p>
          <p>Dropped Frames: ${results.details.frameRate.droppedFrames}</p>
          
          <h4>Memory</h4>
          <p>JS Heap Size: ${(results.summary.totalJSHeapSize / (1024 * 1024)).toFixed(1)} MB</p>
          <p>Used JS Heap: ${(results.summary.usedJSHeapSize / (1024 * 1024)).toFixed(1)} MB</p>
          
          <h4>Long Tasks</h4>
          <p>Count: ${results.details.longTasks.count}</p>
          <p>Total Duration: ${results.details.longTasks.totalDuration.toFixed(1)}ms</p>
          <p>Average Duration: ${results.details.longTasks.avgDuration.toFixed(1)}ms</p>
          <p>Max Duration: ${results.details.longTasks.maxDuration.toFixed(1)}ms</p>
          
          <h4>Network</h4>
          <p>Requests: ${results.details.network.requests}</p>
          <p>Failed Requests: ${results.details.network.failedRequests}</p>
          <p>Total Transfer: ${(results.details.network.totalTransfer / (1024 * 1024)).toFixed(2)} MB</p>
          <p>Average Request Duration: ${results.details.network.avgDuration.toFixed(1)}ms</p>
          
          <h4>DOM</h4>
          <p>Max Nodes: ${results.details.domMetrics.nodes.max}</p>
          <p>Max Depth: ${results.details.domMetrics.depth.max}</p>
          <p>Style Recalculations: ${results.details.domMetrics.styleRecalculations}</p>
          <p>Layout Operations: ${results.details.domMetrics.layoutOperations}</p>
        </div>
      </div>
    `;
  }
  
  /**
   * Display the report in the DOM
   */
  displayReport(results, targetElement) {
    if (!results) {
      console.warn('No results available to display');
      return;
    }
    
    const reportHTML = this.generateHTMLReport(results);
    
    if (typeof targetElement === 'string') {
      targetElement = document.querySelector(targetElement);
    }
    
    if (targetElement && targetElement instanceof HTMLElement) {
      targetElement.innerHTML = reportHTML;
    } else {
      // Create a modal to display the report
      const modal = document.createElement('div');
      modal.className = 'performance-report-modal';
      modal.innerHTML = `
        <div class="modal-content">
          <span class="close-button">&times;</span>
          ${reportHTML}
        </div>
      `;
      
      // Add styles
      const style = document.createElement('style');
      style.textContent = `
        .performance-report-modal {
          position: fixed;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background-color: rgba(0, 0, 0, 0.7);
          z-index: 9999;
          overflow: auto;
        }
        .modal-content {
          background-color: white;
          margin: 5% auto;
          padding: 20px;
          width: 80%;
          max-width: 800px;
          border-radius: 5px;
          box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
        }
        .close-button {
          float: right;
          font-size: 24px;
          font-weight: bold;
          cursor: pointer;
        }
        .performance-report {
          font-family: Arial, sans-serif;
          line-height: 1.5;
        }
        .metrics-summary {
          display: flex;
          flex-wrap: wrap;
          gap: 20px;
          margin-bottom: 20px;
        }
        .metric {
          flex: 1;
          min-width: 200px;
          padding: 15px;
          border-radius: 5px;
          box-shadow: 0 0 5px rgba(0, 0, 0, 0.1);
        }
        .metric.good {
          background-color: #e6f7e6;
          border-left: 4px solid #28a745;
        }
        .metric.warning {
          background-color: #fff3e6;
          border-left: 4px solid #fd7e14;
        }
        .metric h4 {
          margin-top: 0;
          margin-bottom: 10px;
        }
        .metric .value {
          font-size: 24px;
          font-weight: bold;
          margin-bottom: 5px;
        }
        .comparison {
          font-size: 14px;
        }
        .comparison.improved {
          color: #28a745;
        }
        .comparison.degraded {
          color: #dc3545;
        }
        .recommendation {
          margin-bottom: 20px;
          padding: 15px;
          border-radius: 5px;
          box-shadow: 0 0 5px rgba(0, 0, 0, 0.1);
        }
        .recommendation.high {
          background-color: #f8d7da;
          border-left: 4px solid #dc3545;
        }
        .recommendation.medium {
          background-color: #fff3cd;
          border-left: 4px solid #ffc107;
        }
        .recommendation.low {
          background-color: #d1ecf1;
          border-left: 4px solid #17a2b8;
        }
        .recommendation h4 {
          margin-top: 0;
          margin-bottom: 10px;
        }
        .detailed-metrics h4 {
          margin-top: 20px;
          margin-bottom: 10px;
          border-bottom: 1px solid #ddd;
          padding-bottom: 5px;
        }
      `;
      
      document.head.appendChild(style);
      document.body.appendChild(modal);
      
      // Add close button functionality
      const closeButton = modal.querySelector('.close-button');
      closeButton.addEventListener('click', () => {
        document.body.removeChild(modal);
        document.head.removeChild(style);
      });
    }
  }
}

/**
 * Performance Tester class to collect detailed metrics
 */
class PerformanceTester {
  constructor(options = {}) {
    this.options = {
      testDuration: options.testDuration || 30000, // 30 seconds
      sampleInterval: options.sampleInterval || 1000, // 1 second
      testName: options.testName || `perf-test-${Date.now()}`,
      saveResults: options.saveResults !== undefined ? options.saveResults : true,
      ...options
    };
    
    this.isRunning = false;
    this.startTime = 0;
    this.endTime = 0;
    this.sampleCount = 0;
    this.intervalId = null;
    this.longTaskObserver = null;
    this.networkObserver = null;
    this.frameRateMonitor = null;
    
    this.metrics = {
      frameRate: [],
      memory: [],
      longTasks: [],
      networkRequests: [],
      domMetrics: [],
      timestamps: []
    };
    
    this.results = {
      testName: this.options.testName,
      timestamp: new Date().toISOString(),
      testDuration: 0,
      options: this.options,
      summary: {},
      details: {}
    };
  }
  
  /**
   * Start collecting performance metrics
   */
  start() {
    if (this.isRunning) {
      console.warn('Performance test is already running');
      return;
    }
    
    this.isRunning = true;
    this.startTime = performance.now();
    this.sampleCount = 0;
    
    // Reset metrics
    this.metrics = {
      frameRate: [],
      memory: [],
      longTasks: [],
      networkRequests: [],
      domMetrics: [],
      timestamps: []
    };
    
    // Start monitoring frame rate
    this.startFrameRateMonitoring();
    
    // Start monitoring long tasks
    this.startLongTaskMonitoring();
    
    // Start monitoring network requests
    this.startNetworkMonitoring();
    
    // Start collecting samples at regular intervals
    this.intervalId = setInterval(() => {
      this.collectSample();
    }, this.options.sampleInterval);
    
    console.log(`Performance test started: ${this.options.testName}`);
    
    return this;
  }
  
  /**
   * Stop collecting performance metrics and generate results
   */
  stop() {
    if (!this.isRunning) {
      console.warn('No performance test is currently running');
      return null;
    }
    
    this.isRunning = false;
    this.endTime = performance.now();
    
    // Stop interval
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }
    
    // Stop frame rate monitoring
    this.stopFrameRateMonitoring();
    
    // Stop long task monitoring
    this.stopLongTaskMonitoring();
    
    // Stop network monitoring
    this.stopNetworkMonitoring();
    
    // Calculate test duration
    this.results.testDuration = this.endTime - this.startTime;
    
    // Process collected metrics
    this.processMetrics();
    
    console.log(`Performance test completed: ${this.options.testName}`);
    console.log('Results:', this.results);
    
    return this.results;
  }
  
  /**
   * Collect a sample of performance metrics
   */
  collectSample() {
    this.sampleCount++;
    const timestamp = performance.now();
    this.metrics.timestamps.push(timestamp);
    
    // Collect memory metrics if available
    if (performance.memory) {
      this.metrics.memory.push({
        totalJSHeapSize: performance.memory.totalJSHeapSize,
        usedJSHeapSize: performance.memory.usedJSHeapSize,
        jsHeapSizeLimit: performance.memory.jsHeapSizeLimit,
        timestamp
      });
    }
    
    // Collect DOM metrics
    this.metrics.domMetrics.push({
      nodes: document.querySelectorAll('*').length,
      depth: this.getMaxDOMDepth(),
      timestamp
    });
  }
  
  /**
   * Start monitoring frame rate
   */
  startFrameRateMonitoring() {
    let lastFrameTime = performance.now();
    let frameCount = 0;
    let frameTimes = [];
    
    const frameCallback = (timestamp) => {
      if (!this.isRunning) return;
      
      const frameTime = timestamp - lastFrameTime;
      lastFrameTime = timestamp;
      
      // Only count frames that are reasonable (avoid first frame and paused tabs)
      if (frameTime > 0 && frameTime < 1000) {
        frameTimes.push(frameTime);
        frameCount++;
        
        // Calculate FPS over the last second
        const oneSecondAgo = timestamp - 1000;
        while (frameTimes.length > 0 && frameTimes[0] < oneSecondAgo) {
          frameTimes.shift();
        }
        
        const currentFPS = frameTimes.length;
        
        // Record FPS every 16ms (roughly 60fps)
        if (frameCount % 16 === 0) {
          this.metrics.frameRate.push({
            fps: currentFPS,
            frameTime,
            timestamp
          });
        }
      }
      
      requestAnimationFrame(frameCallback);
    };
    
    this.frameRateMonitor = { frameCallback, lastFrameTime, frameCount, frameTimes };
    requestAnimationFrame(frameCallback);
  }
  
  /**
   * Stop monitoring frame rate
   */
  stopFrameRateMonitoring() {
    this.frameRateMonitor = null;
  }
  
  /**
   * Start monitoring long tasks
   */
  startLongTaskMonitoring() {
    if (typeof PerformanceObserver !== 'undefined' && 
        PerformanceObserver.supportedEntryTypes && 
        PerformanceObserver.supportedEntryTypes.includes('longtask')) {
      
      this.longTaskObserver = new PerformanceObserver((entries) => {
        entries.getEntries().forEach((entry) => {
          this.metrics.longTasks.push({
            duration: entry.duration,
            startTime: entry.startTime,
            name: entry.name,
            timestamp: performance.now()
          });
        });
      });
      
      this.longTaskObserver.observe({ entryTypes: ['longtask'] });
    }
  }
  
  /**
   * Stop monitoring long tasks
   */
  stopLongTaskMonitoring() {
    if (this.longTaskObserver) {
      this.longTaskObserver.disconnect();
      this.longTaskObserver = null;
    }
  }
  
  /**
   * Start monitoring network requests
   */
  startNetworkMonitoring() {
    if (typeof PerformanceObserver !== 'undefined' && 
        PerformanceObserver.supportedEntryTypes && 
        PerformanceObserver.supportedEntryTypes.includes('resource')) {
      
      this.networkObserver = new PerformanceObserver((entries) => {
        entries.getEntries().forEach((entry) => {
          this.metrics.networkRequests.push({
            name: entry.name,
            initiatorType: entry.initiatorType,
            duration: entry.duration,
            transferSize: entry.transferSize,
            startTime: entry.startTime,
            timestamp: performance.now()
          });
        });
      });
      
      this.networkObserver.observe({ entryTypes: ['resource'] });
    }
  }
  
  /**
   * Stop monitoring network requests
   */
  stopNetworkMonitoring() {
    if (this.networkObserver) {
      this.networkObserver.disconnect();
      this.networkObserver = null;
    }
  }
  
  /**
   * Get the maximum depth of the DOM tree
   */
  getMaxDOMDepth() {
    const getDepth = (element) => {
      if (!element.children || element.children.length === 0) {
        return 1;
      }
      
      let maxChildDepth = 0;
      for (let i = 0; i < element.children.length; i++) {
        const childDepth = getDepth(element.children[i]);
        maxChildDepth = Math.max(maxChildDepth, childDepth);
      }
      
      return maxChildDepth + 1;
    };
    
    return getDepth(document.documentElement);
  }
  
  /**
   * Process collected metrics and generate summary and details
   */
  processMetrics() {
    // Process frame rate metrics
    const frameRateMetrics = this.metrics.frameRate;
    if (frameRateMetrics.length > 0) {
      const fpsList = frameRateMetrics.map(m => m.fps);
      const frameTimeList = frameRateMetrics.map(m => m.frameTime);
      
      this.results.details.frameRate = {
        min: Math.min(...fpsList),
        max: Math.max(...fpsList),
        avg: fpsList.reduce((sum, fps) => sum + fps, 0) / fpsList.length,
        droppedFrames: frameTimeList.filter(time => time > 16.7).length
      };
      
      this.results.summary.avgFrameRate = this.results.details.frameRate.avg;
    } else {
      this.results.summary.avgFrameRate = 0;
    }
    
    // Process memory metrics
    if (this.metrics.memory.length > 0) {
      const memoryMetrics = this.metrics.memory;
      const totalJSHeapSize = memoryMetrics[memoryMetrics.length - 1].totalJSHeapSize;
      const usedJSHeapSize = memoryMetrics[memoryMetrics.length - 1].usedJSHeapSize;
      
      this.results.summary.totalJSHeapSize = totalJSHeapSize;
      this.results.summary.usedJSHeapSize = usedJSHeapSize;
    } else {
      this.results.summary.totalJSHeapSize = 0;
      this.results.summary.usedJSHeapSize = 0;
    }
    
    // Process long task metrics
    if (this.metrics.longTasks.length > 0) {
      const longTasks = this.metrics.longTasks;
      const totalDuration = longTasks.reduce((sum, task) => sum + task.duration, 0);
      const avgDuration = totalDuration / longTasks.length;
      const maxDuration = Math.max(...longTasks.map(task => task.duration));
      
      this.results.details.longTasks = {
        count: longTasks.length,
        totalDuration,
        avgDuration,
        maxDuration
      };
    } else {
      this.results.details.longTasks = {
        count: 0,
        totalDuration: 0,
        avgDuration: 0,
        maxDuration: 0
      };
    }
    
    // Process network request metrics
    if (this.metrics.networkRequests.length > 0) {
      const networkRequests = this.metrics.networkRequests;
      const totalTransfer = networkRequests.reduce((sum, req) => sum + req.transferSize, 0);
      const avgDuration = networkRequests.reduce((sum, req) => sum + req.duration, 0) / networkRequests.length;
      const failedRequests = networkRequests.filter(req => req.initiatorType === 'fetch' && req.name.includes('!~!')).length;
      
      this.results.details.network = {
        requests: networkRequests.length,
        failedRequests,
        totalTransfer,
        avgDuration
      };
    } else {
      this.results.details.network = {
        requests: 0,
        failedRequests: 0,
        totalTransfer: 0,
        avgDuration: 0
      };
    }
    
    // Process DOM metrics
    if (this.metrics.domMetrics.length > 0) {
      const domMetrics = this.metrics.domMetrics;
      const maxNodes = Math.max(...domMetrics.map(metric => metric.nodes));
      const maxDepth = Math.max(...domMetrics.map(metric => metric.depth));
      const styleRecalculations = domMetrics.filter(metric => metric.styleRecalculations).length;
      const layoutOperations = domMetrics.filter(metric => metric.layoutOperations).length;
      
      this.results.details.domMetrics = {
        nodes: {
          max: maxNodes
        },
        depth: {
          max: maxDepth
        },
        styleRecalculations,
        layoutOperations
      };
    } else {
      this.results.details.domMetrics = {
        nodes: {
          max: 0
        },
        depth: {
          max: 0
        },
        styleRecalculations: 0,
        layoutOperations: 0
      };
    }
  }
}
