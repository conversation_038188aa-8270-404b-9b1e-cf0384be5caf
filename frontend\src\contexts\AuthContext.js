import React, { createContext, useContext, useState, useEffect } from 'react';
import {
  login as authLogin,
  register as authRegister,
  logout as authLogout,
  getUser,
  getToken,
  isAuthenticated as checkAuthenticated
} from '../utils/auth';
import { useAnalytics } from '../components/analytics';

// Create context
export const AuthContext = createContext({
  user: null,
  isAuthenticated: false,
  isLoading: true,
  login: () => { },
  register: () => { },
  logout: () => { },
  hasRole: () => { },
  hasPermission: () => { },
});

/**
 * AuthProvider component
 * Provides authentication state and functions throughout the application
 */
export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const { trackEvent } = useAnalytics();

  // Initialize authentication state
  useEffect(() => {
    const initAuth = async () => {
      try {
        // Check if user is already authenticated
        if (checkAuthenticated()) {
          // Get current user
          const currentUser = getUser();
          const token = getToken();

          if (currentUser) {
            setUser(currentUser);

            // Track authentication
            trackEvent('auth_initialized', {
              userId: currentUser.id || currentUser.username,
              username: currentUser.username,
            });
          }
        }
      } catch (error) {
        console.error('Auth initialization error:', error);
      } finally {
        setIsLoading(false);
      }
    };

    initAuth();
  }, [trackEvent]);

  // Login function
  const login = async (username, password) => {
    setIsLoading(true);

    try {
      const result = await authLogin(username, password);

      if (result.success) {
        setUser(result.user);

        // Track login
        trackEvent('auth_login', {
          userId: result.user.id || result.user.username,
          username: result.user.username,
        });

        return result;
      } else {
        // Track login error
        trackEvent('auth_login_error', {
          error: result.error,
        });

        throw new Error(result.error);
      }
    } catch (error) {
      // Track login error
      trackEvent('auth_login_error', {
        error: error.message,
      });

      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  // Register function
  const register = async (userData) => {
    setIsLoading(true);

    try {
      const result = await authRegister(userData);

      if (result.success) {
        setUser(result.user);

        // Track registration
        trackEvent('auth_register', {
          userId: result.user.id || result.user.username,
          username: result.user.username,
        });

        return result;
      } else {
        // Track registration error
        trackEvent('auth_register_error', {
          error: result.error,
        });

        throw new Error(result.error);
      }
    } catch (error) {
      // Track registration error
      trackEvent('auth_register_error', {
        error: error.message,
      });

      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  // Logout function
  const logout = async () => {
    setIsLoading(true);

    try {
      const result = await authLogout();
      setUser(null);

      // Track logout
      trackEvent('auth_logout');

      return result.success !== false; // Default to true if no explicit success field
    } catch (error) {
      console.error('Logout error:', error);
      setUser(null); // Clear user even if logout fails
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  // Check if user has a specific role
  const hasRole = (role) => {
    return user && user.roles && user.roles.includes(role);
  };

  // Check if user has a specific permission
  const hasPermission = (permission) => {
    return user && user.permissions && user.permissions.includes(permission);
  };

  // Create context value
  const contextValue = {
    user,
    isAuthenticated: !!user,
    isLoading,
    login,
    register,
    logout,
    hasRole,
    hasPermission,
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
};

/**
 * Custom hook for using authentication
 * @returns {Object} Authentication context
 */
export const useAuth = () => {
  return useContext(AuthContext);
};

export default AuthProvider;
