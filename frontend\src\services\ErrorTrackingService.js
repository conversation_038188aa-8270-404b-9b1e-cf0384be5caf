/**
 * Error Tracking Service
 *
 * This service provides error tracking and monitoring functionality.
 * It uses error-stack-parser to parse error stacks into structured data.
 */
import { enhanceError } from '../utils/errorStackUtils';
import csrfService from './csrfService';

// Import DEBUG from config or use a default value
let DEBUG = false;
try {
  const env = require('../config/env');
  DEBUG = env.DEBUG;
} catch (e) {
  // If the import fails, use the default value
  DEBUG = process.env.NODE_ENV === 'development';
}

// Default configuration - DISABLED to prevent conflicts with main errorTracker.js
const defaultConfig = {
  enabled: false, // Disabled to prevent conflicts with main errorTracker.js
  captureUnhandledRejections: false,
  captureUncaughtExceptions: false,
  captureConsoleErrors: false,
  captureNetworkErrors: false,
  breadcrumbsLimit: 100,
  errorLimit: 50,
  reportingEndpoint: '/api/errors',
  reportingInterval: 10000, // 10 seconds
  reportingThreshold: 5, // Report after 5 errors
  samplingRate: 1.0, // Report 100% of errors
  ignoredErrors: [
    'ResizeObserver loop limit exceeded',
    'ResizeObserver loop completed with undelivered notifications',
    'Script error',
    'Network request failed',
    'Failed to fetch',
    'Load failed',
    'Request aborted',
    'Request timed out',
    'Request failed',
    'Network error',
    'AbortError',
    'TypeError: Failed to fetch',
    'TypeError: NetworkError when attempting to fetch resource',
    'TypeError: The network connection was lost',
    'TypeError: Network request failed',
    'TypeError: Load failed',
    'TypeError: Request aborted',
    'TypeError: Request timed out',
    'TypeError: Request failed',
    'TypeError: Network error',
    'TypeError: AbortError'
  ],
  ignoredUrls: [
    'localhost',
    '127.0.0.1',
    'chrome-extension://',
    'moz-extension://',
    'safari-extension://',
    'extension://',
    'chrome://',
    'about:',
    'file://'
  ]
};

/**
 * Error Tracking Service
 */
class ErrorTrackingService {
  /**
   * Create a new ErrorTrackingService instance
   * @param {Object} config - Configuration options
   */
  constructor(config = {}) {
    // Merge default config with provided config
    this.config = { ...defaultConfig, ...config };

    // Initialize state
    this.initialized = false;
    this.breadcrumbs = [];
    this.errors = [];
    this.reportingTimer = null;
    this.originalConsoleError = console.error;
    this.originalWindowOnError = window.onerror;
    this.originalWindowOnUnhandledRejection = window.onunhandledrejection;

    // Bind methods
    this.init = this.init.bind(this);
    this.captureError = this.captureError.bind(this);
    this.captureBreadcrumb = this.captureBreadcrumb.bind(this);
    this.handleConsoleError = this.handleConsoleError.bind(this);
    this.handleWindowError = this.handleWindowError.bind(this);
    this.handleUnhandledRejection = this.handleUnhandledRejection.bind(this);
    this.handleNetworkError = this.handleNetworkError.bind(this);
    this.reportErrors = this.reportErrors.bind(this);
    this.clearErrors = this.clearErrors.bind(this);
    this.destroy = this.destroy.bind(this);
  }

  /**
   * Initialize the error tracking service
   */
  init() {
    if (this.initialized) {
      return;
    }

    // Set up error handlers
    if (this.config.captureConsoleErrors) {
      console.error = this.handleConsoleError;
    }

    if (this.config.captureUncaughtExceptions) {
      window.onerror = this.handleWindowError;
    }

    if (this.config.captureUnhandledRejections) {
      window.onunhandledrejection = this.handleUnhandledRejection;
    }

    if (this.config.captureNetworkErrors) {
      this.setupNetworkErrorCapture();
    }

    // Set up reporting timer
    if (this.config.reportingInterval > 0) {
      this.reportingTimer = setInterval(this.reportErrors, this.config.reportingInterval);
    }

    this.initialized = true;

    if (process.env.NODE_ENV === 'development') {
      console.log('Error tracking service initialized');
    }
  }

  /**
   * Capture an error
   * @param {Error|Object} error - The error to capture
   * @param {Object} context - Additional context for the error
   * @returns {string} - The error ID
   */
  captureError(error, context = {}) {
    if (!this.config.enabled) {
      return null;
    }

    // Check if we should ignore this error
    if (this.shouldIgnoreError(error)) {
      return null;
    }

    // Apply sampling rate
    if (Math.random() > this.config.samplingRate) {
      return null;
    }

    // Enhance the error with parsed stack information
    const enhancedError = enhanceError(error);

    // Create error object
    const errorObject = {
      id: this.generateErrorId(),
      timestamp: Date.now(),
      message: enhancedError.message,
      stack: enhancedError.stack,
      parsedStack: enhancedError.parsedStack,
      formattedStack: enhancedError.formattedStack,
      type: enhancedError.name,
      fileName: enhancedError.fileName,
      lineNumber: enhancedError.lineNumber,
      columnNumber: enhancedError.columnNumber,
      functionName: enhancedError.functionName,
      url: window.location.href,
      userAgent: navigator.userAgent,
      breadcrumbs: [...this.breadcrumbs],
      context: {
        ...context,
        ...this.getErrorContext(error)
      }
    };

    // Add error to queue
    this.errors.push(errorObject);

    // Limit the number of errors
    if (this.errors.length > this.config.errorLimit) {
      this.errors = this.errors.slice(-this.config.errorLimit);
    }

    // Report errors if threshold is reached
    if (this.errors.length >= this.config.reportingThreshold) {
      this.reportErrors();
    }

    if (DEBUG) {
      console.debug('Error captured:', errorObject);
    }

    return errorObject.id;
  }

  /**
   * Capture a breadcrumb
   * @param {Object} breadcrumb - The breadcrumb to capture
   * @returns {Object} - The captured breadcrumb
   */
  captureBreadcrumb(breadcrumb) {
    if (!this.config.enabled) {
      return null;
    }

    // Create breadcrumb object
    const breadcrumbObject = {
      timestamp: Date.now(),
      ...breadcrumb
    };

    // Add breadcrumb to queue
    this.breadcrumbs.push(breadcrumbObject);

    // Limit the number of breadcrumbs
    if (this.breadcrumbs.length > this.config.breadcrumbsLimit) {
      this.breadcrumbs = this.breadcrumbs.slice(-this.config.breadcrumbsLimit);
    }

    return breadcrumbObject;
  }

  /**
   * Handle console.error calls
   * @param {...any} args - The arguments passed to console.error
   */
  handleConsoleError(...args) {
    // Call the original console.error
    this.originalConsoleError.apply(console, args);

    // Capture the error
    const error = args[0];

    if (error instanceof Error) {
      this.captureError(error, { source: 'console.error' });
    } else {
      this.captureError(new Error(args.join(' ')), { source: 'console.error' });
    }
  }

  /**
   * Handle window.onerror events
   * @param {string} message - The error message
   * @param {string} source - The source URL
   * @param {number} lineno - The line number
   * @param {number} colno - The column number
   * @param {Error} error - The error object
   * @returns {boolean} - Whether the error was handled
   */
  handleWindowError(message, source, lineno, colno, error) {
    // Call the original window.onerror
    if (this.originalWindowOnError) {
      this.originalWindowOnError.apply(window, [message, source, lineno, colno, error]);
    }

    // Capture the error
    if (error) {
      this.captureError(error, { source: 'window.onerror' });
    } else {
      this.captureError(new Error(message), {
        source: 'window.onerror',
        url: source,
        line: lineno,
        column: colno
      });
    }

    // Return false to allow the error to propagate
    return false;
  }

  /**
   * Handle window.onunhandledrejection events
   * @param {PromiseRejectionEvent} event - The unhandled rejection event
   */
  handleUnhandledRejection(event) {
    // Call the original window.onunhandledrejection
    if (this.originalWindowOnUnhandledRejection) {
      this.originalWindowOnUnhandledRejection.apply(window, [event]);
    }

    // Capture the error
    const reason = event.reason;

    if (reason instanceof Error) {
      this.captureError(reason, { source: 'unhandledrejection' });
    } else {
      this.captureError(new Error(String(reason)), { source: 'unhandledrejection' });
    }
  }

  /**
   * Handle network errors
   * @param {Error} error - The network error
   * @param {Object} request - The request object
   * @param {Object} response - The response object
   */
  handleNetworkError(error, request, response) {
    this.captureError(error, {
      source: 'network',
      request: {
        url: request.url,
        method: request.method,
        headers: request.headers,
        body: request.body
      },
      response: response ? {
        status: response.status,
        statusText: response.statusText,
        headers: response.headers
      } : null
    });
  }

  /**
   * Set up network error capture
   */
  setupNetworkErrorCapture() {
    // Patch fetch
    const originalFetch = window.fetch;

    // Store the original fetch for error reporting
    window.__originalFetch = originalFetch;

    window.fetch = async (input, init) => {
      // Extract URL from input
      const url = typeof input === 'string' ? input : input.url;

      try {
        const response = await originalFetch(input, init);

        if (!response.ok) {
          this.handleNetworkError(
            new Error(`HTTP error ${response.status}: ${response.statusText}`),
            { url, method: init?.method || 'GET', headers: init?.headers, body: init?.body },
            response
          );
        }

        return response;
      } catch (error) {
        this.handleNetworkError(
          error,
          { url, method: init?.method || 'GET', headers: init?.headers, body: init?.body },
          null
        );

        throw error;
      }
    };

    // Patch XMLHttpRequest
    const originalXhrOpen = XMLHttpRequest.prototype.open;
    const originalXhrSend = XMLHttpRequest.prototype.send;

    XMLHttpRequest.prototype.open = function (method, url, ...args) {
      this._errorTracking = {
        method,
        url
      };

      return originalXhrOpen.apply(this, [method, url, ...args]);
    };

    XMLHttpRequest.prototype.send = function (body) {
      const xhr = this;

      xhr._errorTracking.body = body;

      const originalOnreadystatechange = xhr.onreadystatechange;

      xhr.onreadystatechange = function () {
        if (xhr.readyState === 4) {
          if (xhr.status >= 400) {
            // Use the global reference to the ErrorTrackingService instance
            window.__errorTrackingService.handleNetworkError(
              new Error(`HTTP error ${xhr.status}: ${xhr.statusText}`),
              { ...xhr._errorTracking },
              { status: xhr.status, statusText: xhr.statusText, headers: xhr.getAllResponseHeaders() }
            );
          }
        }

        if (originalOnreadystatechange) {
          originalOnreadystatechange.apply(xhr, arguments);
        }
      };

      return originalXhrSend.apply(xhr, [body]);
    };
  }

  /**
   * Report errors to the server
   */
  reportErrors() {
    if (!this.config.enabled || this.errors.length === 0) {
      return;
    }

    // Clone errors to report
    const errorsToReport = [...this.errors];

    // Clear errors
    this.clearErrors();

    // Get CSRF headers and report errors
    csrfService.getHeaders()
      .then(csrfHeaders => {
        // Report errors using the original fetch to avoid infinite loops
        const originalFetch = window.__originalFetch || window.fetch;

        originalFetch(this.config.reportingEndpoint, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            ...csrfHeaders
          },
          body: JSON.stringify({
            errors: errorsToReport
          }),
          credentials: 'same-origin'
        }).catch(error => {
          // Always log error reporting failures, but only in development
          if (process.env.NODE_ENV === 'development') {
            console.error('Error reporting failed:', error);
          }
        });
      })
      .catch(error => {
        console.warn('Failed to get CSRF token for error reporting:', error);
        // Try to report without CSRF token as fallback
        const originalFetch = window.__originalFetch || window.fetch;

        originalFetch(this.config.reportingEndpoint, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            errors: errorsToReport
          }),
          credentials: 'same-origin'
        }).catch(() => {
          // Silent fail in production
        });
      });
  }

  /**
   * Clear all errors
   */
  clearErrors() {
    this.errors = [];
  }

  /**
   * Destroy the error tracking service
   */
  destroy() {
    if (!this.initialized) {
      return;
    }

    // Restore original handlers
    if (this.config.captureConsoleErrors) {
      console.error = this.originalConsoleError;
    }

    if (this.config.captureUncaughtExceptions) {
      window.onerror = this.originalWindowOnError;
    }

    if (this.config.captureUnhandledRejections) {
      window.onunhandledrejection = this.originalWindowOnUnhandledRejection;
    }

    // Clear reporting timer
    if (this.reportingTimer) {
      clearInterval(this.reportingTimer);
      this.reportingTimer = null;
    }

    // Report any remaining errors
    this.reportErrors();

    this.initialized = false;

    if (process.env.NODE_ENV === 'development') {
      console.log('Error tracking service destroyed');
    }
  }

  /**
   * Check if an error should be ignored
   * @param {Error|Object} error - The error to check
   * @returns {boolean} - Whether the error should be ignored
   */
  shouldIgnoreError(error) {
    const message = this.getErrorMessage(error);

    // Check if the error message is in the ignored errors list
    if (this.config.ignoredErrors.some(ignoredError => message.includes(ignoredError))) {
      return true;
    }

    // Check if the error URL is in the ignored URLs list
    const stack = this.getErrorStack(error);

    if (stack && this.config.ignoredUrls.some(ignoredUrl => stack.includes(ignoredUrl))) {
      return true;
    }

    return false;
  }

  /**
   * Get the error message
   * @param {Error|Object} error - The error
   * @returns {string} - The error message
   */
  getErrorMessage(error) {
    if (error instanceof Error) {
      return error.message;
    }

    if (typeof error === 'string') {
      return error;
    }

    if (error && error.message) {
      return error.message;
    }

    return String(error);
  }

  /**
   * Get the error stack
   * @param {Error|Object} error - The error
   * @returns {string} - The error stack
   */
  getErrorStack(error) {
    if (error instanceof Error) {
      return error.stack;
    }

    if (error && error.stack) {
      return error.stack;
    }

    return '';
  }

  /**
   * Get the error type
   * @param {Error|Object} error - The error
   * @returns {string} - The error type
   */
  getErrorType(error) {
    if (error instanceof Error) {
      return error.name;
    }

    if (error && error.name) {
      return error.name;
    }

    return 'Error';
  }

  /**
   * Get the error context
   * @param {Error|Object} error - The error
   * @returns {Object} - The error context
   */
  getErrorContext(error) {
    const context = {};

    // Add error properties
    if (error && typeof error === 'object') {
      Object.keys(error).forEach(key => {
        if (key !== 'message' && key !== 'stack' && key !== 'name') {
          try {
            context[key] = error[key];
          } catch (e) {
            context[key] = 'Error getting property';
          }
        }
      });
    }

    return context;
  }

  /**
   * Generate a unique error ID
   * @returns {string} - The error ID
   */
  generateErrorId() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, c => {
      const r = Math.random() * 16 | 0;
      const v = c === 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  }
}

// Create singleton instance
const errorTrackingService = new ErrorTrackingService();

// Export singleton instance
export default errorTrackingService;

// Make errorTrackingService available globally for XMLHttpRequest patch
if (typeof window !== 'undefined') {
  window.__errorTrackingService = errorTrackingService;
}

